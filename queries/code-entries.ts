import { useQuery } from '@tanstack/vue-query';
import ListType from '~/enums/listType';


/**
 * Hook for fetching code entries depending on the list type.
 * @param type Single list type or an array of list types.
 */
export function useCodeEntries(type: ListType | ListType[]) {
    const queryKey = Array.isArray(type) ? ['code-entries', type.join(',')] : ['code-entries', type];
    const queryFn = () => Array.isArray(type)
        ? $fetch(`/api/code-entries/`, { method: 'get', query: { type } })
        : $fetch(`/api/code-entries/${type}`);

    return useQuery({
        queryKey,
        queryFn,
        select: (data: ApiModel<'CodeEntry'>[]) => useModelFactory('CodeEntry').fromJson(data),
        placeholderData: [],
        staleTime: Infinity,
        cacheTime: Infinity
    });
}

function scremingSnakeCasetoCamelCase(input: string): string {
    return input
        .toLowerCase()
        .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

export function useInternalPublicationCodeEntries(codeEntry: string, event: ApiModel<"Event">) {

    const queryKey = ['internal-publication-code-entries', event.id, codeEntry];
    const queryFn = () => $fetch(`/api/events/${event.id}/internal-publication/code-entries`, { method: 'get', query: { fields: codeEntry } })
    return useQuery({
        queryKey,
        queryFn,
        select: (data: ApiModel<'InternalPublication'>) => useModelFactory('CodeEntry').fromJson(data[scremingSnakeCasetoCamelCase(codeEntry)]),
        placeholderData: () => ({
            [scremingSnakeCasetoCamelCase(codeEntry)]: [],
        }),
        staleTime: Infinity,
        cacheTime: Infinity
    });
}

