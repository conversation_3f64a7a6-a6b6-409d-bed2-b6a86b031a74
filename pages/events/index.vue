<template>
    <div class="flex flex-col flex-1 w-full gap-y-4">
        <div class="flex flex-wrap items-end flex-none px-4 gap-x-2 gap-y-2 sm:px-0 lg:flex-nowrap lg:gap-y-0">
            <EventFilterInterval v-model="filter" />

            <button class="items-center form-button button-outline" @click="openFilterSettings">
                <IconAdjustments class="w-4 h-4" />
                Filter
            </button>
            <button class="items-center form-button button-outline" @click="refetch()">
                <IconRefresh class="w-4 h-4" />
                Neu laden
            </button>
            <button v-if="$user.canCreateEvent.value" class="form-button button-contained sm:ml-auto" @click="createEvent">
                <IconPlus class="inline w-4 h-4" />
                Ereignis anlegen
            </button>
        </div>

        <EventFilterSummary @adjust-settings="openFilterSettings" v-model="filter" class="flex flex-none bg-white empty:hidden" />

        <UiLoader :is-loading="isFetching">
            <div ref="listEl" class="flex flex-1 w-full transition-all duration-150 bg-white">
                <div v-if="!isFetching && !isSuccess" class="flex flex-col items-center flex-grow p-5 text-center text-softred-900 bg-softred-50">
                    <h3 class="text-lg">Ladehemmung</h3>
                    <p>Beim Anzeigen der Ereignisse läuft gerade etwas schief. Bitte lade die Seite neu oder melde dich einmal ab und wieder an.</p>
                </div>
                <div v-if="isSuccess && events.length > 0" class="flex-1 w-full">
                    <EventList :events="events" @eventsSlected="updateSelectedEvents" />
                </div>
                <div v-else-if="isSuccess" class="flex flex-col items-center flex-grow p-5 text-center text-softred-900 bg-softred-50">
                    <h3 class="text-lg">Keine Ereignisse vorhanden</h3>
                    <p>
                        Zu diesem Filter gibt es keine passenden Ereignisse. Du kannst den Filter anpassen, zum Beispiel den ein oder anderen Eintrag
                        weglassen oder den Zeitraum verändern. Vielleicht bringt dich das an dein Ziel.
                    </p>
                </div>
            </div>
        </UiLoader>

        <div class="flex flex-col items-center justify-end flex-none mr-2 inset-x gap-x-2 gap-y-4 sm:mr-0 sm:flex-row sm:gap-y-0">
            <template v-if="pageCount > 1">
                <UiPaginationSlider v-model="page" :page-count="pageCount" class="flex-1" />
            </template>
            <UiFormField label="Ereignisse pro Seite" :disabled="isFetching">
                <div class="flex gap-x-2">
                    <UiRadioInput v-model="pageSize" :value="10" label="10" />
                    <UiRadioInput v-model="pageSize" :value="20" label="20" />
                    <UiRadioInput v-model="pageSize" :value="50" label="50" />
                </div>
            </UiFormField>
        </div>

        <EventCreateDialog :controller="dialog.createEvent" />
        <EventFilterComposer :controller="dialog.adjustFilterSettings" :filter="filter" />
    </div>
</template>

<script setup lang="ts">

    definePageMeta({
        title: 'Ereignisse',
        middleware: ['requires-auth', 'redirects']
    })

    const router = useRouter()

    const filters = useEventFilters()

    const { events, filter, pageSize, isFetching, isSuccess, pageCount, page, refetch} = useEventList(filters)

    const listEl = ref<HTMLElement>()

    const dialog = {
        createEvent: useDialogController('createEvent'),
        adjustFilterSettings: useDialogController('adjustFilterSettings')
    }

    const { createEvent: commitCreatedEvent } = useCreateEvent()

    const runWithNotification = useRunWithNotification()

    async function createEvent() {

        const { data, isCanceled } = await dialog.createEvent.reveal()

        if (!isCanceled) {
            const event = await runWithNotification(() => commitCreatedEvent({data}), {
                pending: 'Das neue Ereignis wird gespeichert',
                error: 'Beim Speichern des Erignisses ist leider ein Fehler aufgetreten'
            })

            if (event instanceof Error) {
                return
            }

            router.push({ name: 'events-id-details', params: { id: event.id } })
        }
    }

    async function openFilterSettings() {
        const { data, isCanceled } = await dialog.adjustFilterSettings.reveal(filter.value)

        if (!isCanceled) {
            filter.value = data
        }
    }

    // Select multiple events (for the event action selector)
    const selectedEvents = ref([])
    const updateSelectedEvents = function (se) {
        selectedEvents.value = Object.keys(se).filter((id) => se[id])
    }
</script>
