import type { C<PERSON>Node, ClusterOptions, CommonRedisOptions, RedisOptions, StandaloneConnectionOptions } from 'ioredis'
import type { CookieSerializeOptions } from 'cookie-es'
import { defineNuxtModule, addServer<PERSON><PERSON><PERSON>, useNit<PERSON>, createResolver, extendViteConfig, extendPages } from '@nuxt/kit'

export interface KeycloakOptions {
    /**
     * @default process.env.KEYCLOAK_URL
     * @example 'https://login.drkserver.org/auth'
     * @type string
     */
    url?: string

    /**
     * @default process.env.KEYCLOAK_REALM
     * @example 'drkdemo'
     * @type string
     */
    realm?: string

    /**
     * @default process.env.KEYCLOAK_CLIENT_ID
     * @example 'em-light-confidential'
     * @type string
     */
    clientId?: string

    /**
     * @default process.env.KEYCLOAK_CLIENT_SECRET
     * @example 'secret'
     */
    clientSecret?: string
}

type CookieOptions = {
    cookie?: {
        name: string
        options?: CookieSerializeOptions
    }
}

type StorageOptions =
    | {
          /**
           * The 'default' redis adapter. It needs an url <host>:<port>
           */
          type: 'redis'
          storage: {
              url: string
              options?: CommonRedisOptions & StandaloneConnectionOptions
          }
      }
    | {
          /**
           * Redis in _cluster_ mode.
           */
          type: 'cluster'
          storage: {
              nodes: ClusterNode[]
              options?: ClusterOptions
          }
      }
    | {
          /**
           * Redis in _sentinel_ mode.
           */
          type: 'sentinel'
          storage: {
              options: RedisOptions
          }
      }

type SessionOptions = StorageOptions & CookieOptions

export interface ModuleOptions {
    keycloak?: KeycloakOptions
    session?: SessionOptions
}

const { resolve } = createResolver(import.meta.url)

export default defineNuxtModule<ModuleOptions>({
    meta: {
        name: '@mogic/nuxt-auth-openid',
        configKey: 'auth'
    },

    defaults: {
        keycloak: {
            url: 'https://login.drkserver.org/auth',
            realm: 'drkdemo',
            clientId: 'em-light-confidential',
            clientSecret: process.env.KEYCLOAK_CLIENT_SECRET
        },
        session: {
            type: 'redis',
            storage: {
                url: '127.0.0.1:6379',
                options: {
                    keyPrefix: 'em-light'
                }
            },
            cookie: {
                name: 'em-light-auth',
                options: {
                    path: '/',
                    httpOnly: true
                }
            }
        }
    },

    async setup(options, nuxt) {
        nuxt.options.runtimeConfig.keycloak = options.keycloak
        nuxt.options.runtimeConfig.authSession = options.session

        nuxt.options.alias = Object.assign(nuxt.options.alias, {
            '#openid': resolve('./utils/index')
        })

        // Auto-Import `useAuthClient` and `useAuthContext`
        nuxt.hook('ready', async () => {
            const nitro = useNitro()
            await nitro.unimport?.scanImportsFromFile(resolve('./utils/index.ts'))
        })

        /**
         * We have to add a nuxt route for /login in order to make navigateTo work
         */
        extendPages((pages) => {
            pages.push({
                name: 'login',
                path: '/login',
                file: resolve('./pages/login.vue'),
                meta: {
                    guestRoute: true
                }
            })
        })

        addServerHandler({
            route: '/login',
            handler: resolve('./middleware/login')
        })

        addServerHandler({
            route: '/callback',
            handler: resolve('./middleware/callback')
        })

        addServerHandler({
            route: '/logout',
            handler: resolve('./middleware/logout')
        })

        /**
         * Register the refresh middleware
         */
        addServerHandler({ handler: resolve('./middleware/refresh') })

        extendViteConfig((config) => {
            config.optimizeDeps = config.optimizeDeps || {}
            config.optimizeDeps.include = config.optimizeDeps.include || []
            // config.optimizeDeps.include.push('qs')
        })
    }
})

declare module '@nuxt/schema' {
    interface RuntimeConfig {
        keycloak: KeycloakOptions
        authSession: SessionOptions
    }
}
