<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title><PERSON>re<PERSON><PERSON> {{`(${copyData.eventsToCopy.length})`}} </template>

        <template v-if="isRevealed">
            <div class="flex flex-col gap-8 mb-8">
                <UiDisclosure v-model:open="open" class="mb-8">
                    <template #button>
                        <span class="flex items-center gap-4 text-base"> <UiIcon name="information-circle" class="h-5 w-5" /> So geht das </span>
                        <UiIcon
                        name="chevron-down"
                        class="text-gray-900transform h-6 w-6 transition-all"
                        :class="{ 'rotate-180': open, 'rotate-0': !open }" />
                    </template>
                    <div class="flex flex-col gap-4 text-xs">
                        <span>
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras semper eu enim eget aliquet.
                            Aliquam eget laoreet augue. Morbi neque risus, bibendum ac dolor vel, gravida hendrerit nisi
                            Nam dolor odio, efficitur sed eros et, scelerisque elementum mi. Nam luctus libero a maximus porta.
                        </span>
                    </div>
                </UiDisclosure>
                <UiLoader :is-loading="isLoading" class="">
                    <fieldset class="mb-4 flex flex-col gap-y-4">
                        <UiCheckInput label="Ereignisverantwortliche" v-model="copyData.canCopyResponsiblePersons" :disabled="!canCopyResponsiblePersons"/>
                        <UiCheckInput label="Personen aus “Wer darf registrieren?" v-model="copyData.canCopyRegistrators" :disabled="!canCopyRegistrators" />
                        <UiCheckInput label="Planstellen" v-model="copyData.canCopyEventPosts" :disabled="!canCopyEventPosts" />
                        <UiCheckInput label="Einladungen" v-model="copyData.canCopyInvitations" :disabled="!canCopyInvitations"/>
                    </fieldset>
                </UiLoader>

                    <p class="font-bold text-lg">Beginn des neuen Ereignisses</p>

                    <UiFormField v-for="event in copyData.eventsToCopy" :key="event.id" label="Zeitraum: Beginn*" class="flex flex-col">
                        <div class="flex gap-4">
                            <UiDatetimeInput v-model="event.dateFrom" class="w-full"/>
                            <button v-if="copyData.eventsToCopy.length > 1" @click="removeDate(event.id)" class="flex items-center form-button button-sm rounded-md">
                                <UiIcon name="trash" class="h-4 w-4" />
                            </button>
                        </div>
                    </UiFormField>

                    <button class="form-button ml-auto" @click="addDate" :disabled="canAddEvent">+ weitere Kopie hinzufügen</button>
                </div>
            </template>

        <template #footer>
            <div class="flex flex-col gap-4">
                <div class="w-full flex justify-end">
                    <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
                    <button class="form-button button-contained" @click="submit" :disabled="validator.$invalid">Ereignis kopieren</button>
                </div>
            </div>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import { differenceInMilliseconds, addMilliseconds } from 'date-fns'
    import useVuelidate from '@vuelidate/core'
    import { required, helpers } from '@vuelidate/validators'

    type CopyEventData = {
        dresscodes: ApiModel<'CodeEntry'>[],
        caterings: ApiModel<'CodeEntry'>[],
        locations: ApiModel<'EventAddress'>[],
        organizer: ApiModel<'ComplexAddressContact'>
        resourceSettings: ApiModel<'ResourceSetting'>[],
        responsiblePersons: ApiModel<'MemberData'>[],
        canCopyResponsiblePersons: boolean,
        registrators: ApiModel<'MemberData'>[],
        canCopyRegistrators: boolean,
        eventPost: ApiModel<'EventPost'>[],
        canCopyEventPosts: boolean,
        signingUps: ApiModel<'SigningUp'>[],
        eventsToCopy: ApiModel<'Event'>[],
        event: ApiModel<'Event'>,
        canCopyInvitations: boolean,
        internalPublications: ApiModel<'InternalPublication'>[]
    }


    const props = defineProps<{
        event: ApiModel<'Event'>
        controller: DialogController<'copyEvent'>
    }>()

    const { isRevealed, confirm, cancel } = props.controller

    const copyData = ref<CopyEventData>({
        dresscodes: [],
        caterings: [],
        locations: [],
        organizer: null,
        responsiblePersons: [],
        resourceSettings: [],
        canCopyResponsiblePersons: false,
        registrators: [],
        canCopyRegistrators: false,
        eventPost: [],
        canCopyEventPosts: false,
        canCopyInvitations: false,
        signingUps: [],
        internalPublications: [],
        eventsToCopy: [useModelFactory('Event').create({
            ...props.event,
            number: null
        })],
        event: props.event
    })

    const originalEventDuration = differenceInMilliseconds(props.event.dateUpTo, props.event.dateFrom)
    const { canEdit } = inject(EventPermissionsKey)
    const { data: internalPublications, isLoading: isLoadingInternalPublications } = useEventQueries(props.event.id).internalPublication(canEdit.value)

    const events = computed(()=>{
        return copyData.value.eventsToCopy
    })

    const canAddEvent = computed(()=>{
        return copyData.value.eventsToCopy.length > 99
    })

    const rules = computed(() => {
        return {
            events: {
            $each: helpers.forEach({
                dateFrom: { required }
            })
            }
        }
    })

    const validator = useVuelidate(rules, { events: events })

    const open = ref<boolean>(false)
    const { $user } = useNuxtApp()

    const { data: responsiblesData } = useEventQuery('responsibles')
    const { data: registratorsData, isLoading: isLoadingRegistratorsData } = useEventQuery('registrators')
    const { data: eventPostsData } = useEventQuery('eventPosts')
    const { data: signingUpsData, isLoading: isLoadingSigningUpsData  } = useEventQuery('signingUps')
    const { data: cateringsData } = useEventQuery('caterings')
    const { data: dresscodesData } = useEventQuery('dresscodes')
    const { data: locationsData } = useEventQuery('locations')
    const { data: organizerData } = useEventQuery('organizer')
    const { data: resourceSettingsData } = useEventQuery('resourceSettings')

    const responsibles = computed<ApiModel<'MemberData'>[]>(() => {
       return responsiblesData.value ?? props.event.responsibles.map(person => person.masterdata)
    })

    const eventPosts = computed(() => {
        return eventPostsData.value ?? props.event.eventPosts
    })

    const registrators = computed<ApiModel<'MemberData'>[]>(()=>{
        return registratorsData.value ?? []
    })

    const signingUps = computed<ApiModel<'SigningUp'>[]>(()=>{
        return signingUpsData.value ?? props.event.allSigningUps?.items
    })

    const dresscodes = computed<ApiModel<'CodeEntry'>[]>(()=>{
        return dresscodesData.value ?? props.event.dressCodes
    })

    const caterings = computed<ApiModel<'CodeEntry'>[]>(()=>{
        return cateringsData.value ?? props.event.caterings
    })

    const locations = computed<ApiModel<'EventAddress'>[]>(()=>{
        return locationsData.value ?? props.event.locations
    })

    const organizer = computed<ApiModel<'ComplexAddressContact'>>(()=>{
        return organizerData.value ?? props.event.organizer
    })

    const resourceSettings = computed<ApiModel<'ResourceSetting'>[]>(()=>{
        return resourceSettingsData.value ?? []
    })

    const isLoading = computed<boolean>(()=> isLoadingRegistratorsData.value || isLoadingSigningUpsData.value || isLoadingInternalPublications.value)

    const canCopyResponsiblePersons = computed<boolean>(()=>{
        if(responsibles.value.length === 0) return false
        if(responsibles.value.length > 1) return true
        if(responsibles.value[0].id == $user.basicData.id) return false
        return true
    })

    const canCopyRegistrators = computed<boolean>(()=>{
       return registrators.value.length > 0
    })

    const canCopyEventPosts = computed<boolean>(()=>{
       return eventPosts.value.length > 0
    })

    const canCopyInvitations = computed<boolean>(()=>{
       return internalPublications.value?.length > 0
    })

    // add random id just for dealing with data on the frontend. New id will be added by backend after create new event
    function addDate() {
        copyData.value.eventsToCopy = [...copyData.value.eventsToCopy, useModelFactory('Event').create({
            ...props.event,
            number: null,
            id: Math.random(),
            meetingpointTime: props.event.meetingpointTime ? addMilliseconds(props.event.meetingpointTime, originalEventDuration) : null
        }) ]
    }

    function removeDate(id: number) {
        copyData.value.eventsToCopy = copyData.value.eventsToCopy.filter(event => event.id !== id)
    }

    function submit() {
        copyData.value.eventPost = eventPosts.value
        copyData.value.dresscodes = dresscodes.value
        copyData.value.caterings = caterings.value
        copyData.value.registrators = registrators.value
        copyData.value.signingUps = signingUps.value
        copyData.value.resourceSettings = resourceSettings.value
        copyData.value.locations = locations.value
        copyData.value.organizer = organizer.value
        copyData.value.internalPublications = internalPublications.value
        copyData.value.responsiblePersons = responsibles.value.filter(person => person.id !== $user.basicData.id)
        copyData.value.eventsToCopy.map((event, index) => {
            event.dateUpTo = addMilliseconds(event.dateFrom, originalEventDuration)
            event.extendedDescription = `${event.extendedDescription} (${index + 1})`
        })
        confirm(copyData.value)
    }
</script>
