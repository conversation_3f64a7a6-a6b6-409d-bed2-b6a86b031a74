<template>
    <UiComposer :emit="emit">
        <template #title>Do<PERSON><PERSON> hinzufügen</template>

        <template v-if="isCurrentStep('file-select')">
            <UiDropUpload label="W<PERSON>hle eine Datei aus." @select="selectFile" />
        </template>

        <template v-if="isCurrentStep('file-metadata')">
            <EventDocumentsMetadataForm v-model="documentMeta" class="mb-4" />
        </template>
        <template #footer>
            <button v-if="!isLastStep" class="form-button button-contained-secondary" @click="emit('cancel')">Abbrechen</button>
            <button v-if="isPrevious('file-select')" @click="goToPrevious()" class="form-button button-contained-secondary">Zurück</button>
            <button
                class="form-button button-contained"
                :class="{ hidden: currentStep.action === 'Weiter' }"
                :disabled="!currentStep.isValid() || !isCurrentStep('file-metadata')"
                @click="submit">
                {{ currentStep.action }}
            </button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { useStepper } from '@vueuse/core'
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import ListType from '~~/enums/listType'

    const rules = computed(() => {
        const _rules: Record<string, any> = {
            type: { required },
            permission: { required },
            description: { required }
        }

        return _rules
    })

    const props = defineProps({
        eventId: Number,
        confirm: Function,
        cancel: Function
    })

    const uploadedFile = ref(null)
    const documentMeta = ref<ApiModel<'EventDocumentMeta'>>(useModelFactory('EventDocumentMeta').fromJson({}))

    const validator = useVuelidate(rules, documentMeta)

    const emit = defineEmits<{
        (e: 'confirm', value: FormData): void
        (e: 'cancel'): void
    }>()

    const {
        current: currentStep,
        isPrevious,
        goToPrevious,
        isCurrent: isCurrentStep,
        isLast: isLastStep,
        goToNext
    } = useStepper({
        'file-select': {
            title: 'Suche eine Datei',
            action: 'Weiter',
            isValid: () => true // Filetype Check is done on the API side
        },
        'file-metadata': {
            title: 'Datei Hochladen',
            action: 'Speichern',
            isValid: () => !validator.value.$invalid
        }
    })

    function selectFile(selectedFile: File) {
        goToNext()

        uploadedFile.value = selectedFile
        documentMeta.value.fileName = selectedFile.name
    }

    const filenameLabel = computed(() => {
        if (uploadedFile.value.name === documentMeta.value.fileName) {
            return 'Dateiname'
        } else {
            return `Dateiname (${uploadedFile.value.name})`
        }
    })

    async function submit() {
        if (!isLastStep.value) {
            return goToNext()
        }

        // Build a formData object and attach the file and metadata as properties
        const formData = new FormData()
        // Be careful: documentMeta should NOT include fileNameOriginal as causes backend to respond with error about invalid mime type
        formData.append('eventDocument', new Blob([JSON.stringify(unref(documentMeta))], { type: 'application/json' }))
        formData.append('data', unref(uploadedFile))

        emit('confirm', formData)
    }
</script>
