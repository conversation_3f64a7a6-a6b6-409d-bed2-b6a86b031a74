<template>
    <div>
        <form class="grid grid-cols-12 gap-4 p-4 bg-gray-50" @submit.prevent.stop="submitRegistration">
            <UiFormField label="Person wählen*" class="col-span-full md:col-span-5">
                <MemberPicker
                    v-if="!registration.resource.member"
                    @select="(member) => (registration.resource.member = member)"
                    :allowed-scopes="['MY_MEMBERS', 'GLOBAL_MEMBERS']"
                    popper-position="top-start"
                    class="w-full"
                    placeholder="Name, Personalnummer" />
                <div v-else class="flex items-center rounded-sm border bg-white">
                    <ProvideMember :member="registration.resource.member" class="flex-1 text-xs" />
                    <span @click="registration.resource.member = null" class="form-button button-xs ml-2 mr-0.5 flex-none rounded-full p-1">
                        <UiIcon name="x" class="h-4 w-4" />
                    </span>
                </div>
                <button @click="emit('registerExternalPerson')" class="cursor-pointer pt-2 text-sm text-sky-500 no-underline hover:underline">
                    Extern Mitwirkende hinzufügen
                </button>
            </UiFormField>
            <div class="col-span-full flex flex-wrap gap-4">
                <div class="flex flex-col gap-11">
                    <UiFormField label="Anwesend von">
                        <UiDateTimePicker v-model="registration.start" :disabled="setDateFromToNow" class="w-full" />
                    </UiFormField>
                    <UiCheckInput label='kommt jetzt' v-model="setDateFromToNow" class="-mt-9 md:flex col-span-3 col-start-5"/>
                </div>
                 <div class="flex flex-col gap-11">
                    <UiFormField label="Anwesend bis" class="w-full">
                        <UiDateTimePicker v-model="registration.end" :disabled="setDateUpToToNow" class="w-full" />
                    </UiFormField>
                    <UiCheckInput label='geht jetzt' v-model="setDateUpToToNow" class="-mt-9 md:flex col-span-3 col-start-5"/>
                </div>
                <button type="submit" class="w-full md:w-auto hover:cursor-pointer flex justify-center items-center mt-2 md:-mt-3 md:ml-2 self-center md:self-[unset] p-2.5 text-blue-500 hover:bg-blue-50 disabled:text-gray-300" :disabled="v$.$invalid">
                    <UiIcon name="plus" />
                </button>
            </div>
        </form>
    </div>
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required, helpers } from '@vuelidate/validators'

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const emit = defineEmits<{
        (event: 'submit', value: ApiModel<'EventRegistration'>): any
        (event: 'registerExternalPerson'): any
    }>()

    const registration = ref(useModelFactory('EventRegistration').create({
      resource: useModelFactory("Resource").create({
        external: false,
        type: "PERSONAL"
      })
    }))

    const endOrStartNotEmpty = helpers.withMessage(
        'End or start date must be not empty.',
        () => {
            if (isUpdatingStart.value || isUpdatingEnd.value) return true
            return !!registration.value.end || !!registration.value.start
        }
    )

    const endDateAfterStartDate = helpers.withMessage(
        'End date must be after start date.',
        () => {
            if (isUpdatingStart.value && isUpdatingEnd.value) return true
            return !registration.value.end || !registration.value.start || registration.value.end >= registration.value.start
        }
    )

    const v$ = useVuelidate<ApiModel<'EventRegistration'>>(
        {
            resource: {
                member: { required }
            },
            endOrStartNotEmpty,
            endDateAfterStartDate
        },
        registration
    )

    const updateDateFromNowInterval = ref<number | null>(null);
    const updateDateUpToInterval = ref<number | null>(null);
    const isUpdatingStart = ref(false)
    const isUpdatingEnd = ref(false)

    const setDateFromToNow = ref(false)
    const setDateUpToToNow = ref(false)

    watch(setDateFromToNow, (value) => {
        if (value) {
            isUpdatingStart.value = true
            registration.value.start = new Date()
            updateDateFromNowInterval.value = window.setInterval(() => {
                registration.value.start = new Date()
            }, 1000)
        } else {
            isUpdatingStart.value = false
            if (updateDateFromNowInterval.value !== null) {
                clearInterval(updateDateFromNowInterval.value)
                updateDateFromNowInterval.value = null
            }
        }
    })

    watch(setDateUpToToNow, (value) => {
        if (value) {
            isUpdatingEnd.value = true
            registration.value.end = new Date()
            updateDateUpToInterval.value = window.setInterval(() => {
                registration.value.end = new Date()
            }, 1000)
        } else {
            isUpdatingEnd.value = false
            if (updateDateUpToInterval.value !== null) {
                clearInterval(updateDateUpToInterval.value)
                updateDateUpToInterval.value = null
            }
            registration.value.end = null
        }
    })

    onUnmounted(() => {
        if (updateDateFromNowInterval.value !== null) {
            clearInterval(updateDateFromNowInterval.value);
        }
        if (updateDateUpToInterval.value !== null) {
            clearInterval(updateDateUpToInterval.value);
        }
    })

    function submitRegistration() {
        if (v$.value.$invalid) {
            return
        }

        emit('submit', registration.value)

        registration.value = useModelFactory('EventRegistration').create({
            resource: useModelFactory('Resource').create({
                external: false,
                type: 'PERSONAL'
            }),
            start: registration.value.start,
            end: registration.value.end
        })
    }
</script>
