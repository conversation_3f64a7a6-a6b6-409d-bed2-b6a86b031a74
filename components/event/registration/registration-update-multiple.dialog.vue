<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Zeiten ändern</template>

        <p class="mb-4 text-sm font-light">Du bist dabei die Zeiten von {{ registrations.length }} Teilnehmenden/r zu ändern.</p>

        <fieldset class="mb-6 grid grid-cols-1 gap-4">
            <UiCheckInput label='Bearbeite "Anwesend von"' v-model="isDateFromEditable" class="mt-2"/>
            <div>
                <UiFormField label="Anwesend von">
                    <UiDateTimePicker v-model="start" class="w-full" :disabled="!isDateFromEditable || isDateFromNow"/>
                </UiFormField>
                <UiCheckInput label="kommt jetzt" v-model="setDateFromNow" :disabled="!isDateFromEditable" class="mt-2" />
            </div>

            <UiCheckInput label='Bearbeite "Anwesend bis"' v-model="isDateUpToEditable" class="mt-2"/>
            <div>
                <UiFormField label="Anwesend bis">
                    <UiDateTimePicker v-model="end" class="w-full" :disabled="!isDateUpToEditable || isDateUntilNow" />
                </UiFormField>
                <UiCheckInput label="geht jetzt" v-model="setDateUntilNow" :disabled="!isDateUpToEditable" class="mt-2" />
            </div>
        </fieldset>

        <p v-if="isDateFromEditable && isDateUpToEditable" class="mb-4 text-sm font-light">Dauer: {{ $formatDuration(start, end) || 'unbekannt' }}</p>

        <template #footer>
            <button class="form-button button-contained-secondary" @click="cancelUpdate">Abbrechen</button>
            <button class="form-button button-contained" :disabled="v$.$invalid" @click="updateMultiple">Zeiten übernehmen</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import { minBy, maxBy } from 'lodash-es'
    import useVuelidate from '@vuelidate/core'
    import { helpers } from '@vuelidate/validators'

    const props = defineProps<{
        controller: DialogController<'updateMultipleRegistrations'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const registrations = ref<ApiModel<'EventRegistration'>[]>([])

    const start = ref<Date>(null)
    const end = ref<Date>(null)

    const isDateFromNow = ref<boolean>(false)
    const isDateUntilNow = ref<boolean>(false)
    const isDateFromEditable = ref<boolean>(false)
    const isDateUpToEditable = ref<boolean>(false)

    onReveal((input) => {
        registrations.value = input
        start.value = minBy(input, 'start').start
        end.value = maxBy(input, 'end')?.end || null
        isDateFromNow.value = false
        isDateUntilNow.value = false
        isDateFromEditable.value = false
        isDateUpToEditable.value = false
    })

    const setDateFromNow = ref(false)
    const setDateUntilNow = ref(false)

    watch(setDateFromNow, (value) => {
        if (value) {
            start.value = new Date()
        }
        isDateFromNow.value = value
    })

    watch(setDateUntilNow, (value) => {
        if (value) {
            end.value = new Date()
        }
        isDateUntilNow.value = value
    })

    const atLeastOneCheckboxChecked = computed(() => {
        return isDateFromEditable.value || isDateUpToEditable.value
    })

    const atLeastOneRequired = helpers.withMessage(
        'At least one checkbox must be selected.',
        (value) => value === true
    )

    const startRequiredIfEditable = helpers.withMessage(
        'Start date required if editable.',
        () => {
            return !isDateFromEditable.value || start.value !== null
        }
    )

    const endRequiredIfEditable = helpers.withMessage(
        'End date required if editable.',
        () => {
            return !isDateUpToEditable.value || end.value !== null
        }
    )

    const endAfterStart = helpers.withMessage(
        'End date must be after start date.',
        () => {
            return !end.value || !start.value || end.value >= start.value
        }
    )

    // Vuelidate setup:
    const v$ = useVuelidate(
    {
        checkboxGroup: {
            atLeastOneRequired
        },
        start: {
            startRequiredIfEditable
        },
        end: {
            endRequiredIfEditable,
            endAfterStart
        }
    },
        {
            checkboxGroup: atLeastOneCheckboxChecked,
            start,
            end
        }
    )

    function updateMultiple() {
        if (v$.value.$invalid) {
            return
        }

        const newStartDate = isDateFromNow.value ? start.value : null
        const newEndDate = isDateUntilNow.value ? end.value : null

        const updated = registrations.value.map((registration) => {
            return useModelFactory('EventRegistration').create({
                ...registration,
                start: newStartDate ?? (isDateFromEditable.value ? start.value : registration.start),
                end: newEndDate ?? (isDateUpToEditable.value ? end.value : registration.end)
            })
        })

        confirm(updated)
    }

    function cancelUpdate() {
        setDateUntilNow.value = false
        setDateFromNow.value = false
        cancel()
    }
</script>
