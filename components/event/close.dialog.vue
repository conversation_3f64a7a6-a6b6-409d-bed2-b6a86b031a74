<script lang="ts" setup>
    import { UseConfirmDialogReturn } from '@vueuse/core'
    import { useCollectionState } from '~~/composables/ui/collection-state'

    const props = defineProps<{
        controller: UseConfirmDialogReturn<boolean, ApiModel<'MemberData'>[], any>
        event: ApiModel<'Event'>
    }>()

    const router = useRouter()

    const { isApprenticeship } = useEventDescription(props.event)

    const title = computed(() => {
        if (!!isApprenticeship.value) {
            return 'Ausbildung abschließen'
        } else {
            return 'Ereignis abschließen'
        }
    })

    /**
     *  Check in all registered person if registration times are complete to be able to close event
     */
    const { data: _registeredPersons } = useEventQuery('registrations')
    const isRegistrationComplete = computed(() => {
        return _registeredPersons.value.find((person) => person.end === null) ? false : true
    })

    const membersMustBeNotified = computed(() => {
        return isApprenticeship.value
    })

    const { data: registrations, isFetched: registrationsFetched } = useEventQueries(props.event.id).registrations()
    const { data: signingUps, isFetched: signingUpsFetched } = useEventQueries(props.event.id).signingUps()

    const canConfirm = computed(() => {
        return !membersMustBeNotified.value || (registrationsFetched.value && signingUpsFetched.value)
    })

    type IndifferentPersonnelResource =
        | { type: 'internal'; source: 'planning' | 'registration'; id: number; data: ApiModel<'MemberData'> }
        | { type: 'external'; source: 'planning' | 'registration'; id: number; data: ApiModel<'ExternalPerson'> }

    const { $fullName } = useNuxtApp()

    const participants = computed<IndifferentPersonnelResource[]>(() => {
        const planned = signingUps.value
            .filter((signingUp) => {
                return signingUp.resource?.type === 'PERSONAL' && !!signingUp.assignedResourceSettings
            })
            .map((signingUp) => {
                if (signingUp.resource.external) {
                    return {
                        type: 'external' as const,
                        source: 'planning' as const,
                        id: signingUp.resource.externalPerson?.id,
                        data: signingUp.resource.externalPerson
                    }
                } else {
                    return {
                        type: 'internal' as const,
                        source: 'planning' as const,
                        id: signingUp.resource.member?.id,
                        data: signingUp.resource.member
                    }
                }
            })

        const registered = registrations.value.map((person) => {
              if (person.resource.external) {
                    return {
                        type: 'external' as const,
                        source: 'registration' as const,
                        id: person.resource.externalPerson?.id,
                        data: person.resource.externalPerson
                    }
                } else {
                    return {
                        type: 'internal' as const,
                        source: 'registration' as const,
                        id: person.resource.member?.id,
                        data: person.resource.member
                    }
                }
        })

        return [...registered, ...planned].sort(({ data: a }, { data: b }) => a?.firstname.localeCompare(b?.firstname))
    })

    const { collection, selected } = useCollectionState(participants, {
        getKey(participant) {
            return `${participant.type}_${participant.id}`
        },

        getDisplayValue(participant) {
            return $fullName(participant.data)
        },

        disabledCondition(participation) {
            return participation.type === 'external'
        },

        selectedKeys: 'all'
    })

    const filter = ref<'all' | 'planning' | 'registration'>('all')

    const filteredCollection = computed(() => {
        if(filter.value === 'all') {
             return [
                ...collection.value.filter(({ data: participant }) => participant.source === 'registration'),
                ...collection.value.filter(({ data: participant }) => participant.source === 'planning')
            ]
        } else if (filter.value === 'planning') {
            collection.value.filter(({ data: participant }) => participant.source === 'registration').forEach(item => item.unselect());
            return collection.value.filter(({ data: participant }) => participant.source === filter.value)
        } else {
            collection.value.filter(({ data: participant }) => participant.source === 'planning').forEach(item => item.unselect());
            return collection.value.filter(({ data: participant }) => participant.source === filter.value)
        }
    })

    const { isRevealed, cancel, confirm: _confirm } = props.controller

    const messages: { [key: string]: string } = {
        canCloseEvent: `Du bist dabei dieses Ereignis abzuschließen. Für Ausbildungen gilt: Wer die Ausbildung absolviert hat,
            bekommt sie in die Personalakte eingetragen. Gegebenenfalls muss auf dem Dienstweg
            noch ein*e Ereignismanager*in zustimmen. Extern Mitwirkende sind hier nicht aufgeführt.
            Wenn du das Ereignis abschließt, kannst du es nicht mehr bearbeiten. Lesen geht weiterhin,
            Dokumente runterladen auch.`,
        canNotCloseEvent: `Du kannst das Ereignis noch nicht abschließen: Die Bis-Zeit in der Registrierung
            ist noch nicht bei allen Teilnehmenden eingetragen. Wenn du das gemacht hast, kannst du das Ereignis abschließen.`
    }

    function navigateToRegistrations() {
        router.push({ name: 'events-id-registrations', params: { id: props.event.id } })
    }

    function finishRegistration() {
        navigateToRegistrations()
        cancel()
    }

    function confirm() {
        if (membersMustBeNotified.value) {
            const retrieveMemberData = (item: IndifferentPersonnelResource): ApiModel<'MemberData'> => {
                if (item.type === 'internal') {
                    return item.data
                }
            }

            const members = selected.value
                .map((item) => {
                    return retrieveMemberData(item)
                })
                .filter((memberData) => !!memberData)

            _confirm(members)
        } else {
            _confirm([])
        }
    }
</script>

<template>
    <UiDialog :title="title" :is-revealed="isRevealed">

        <p v-if="isRegistrationComplete" class="mb-4">{{ messages.canCloseEvent }}</p>
        <p v-else class="mb-4">{{ messages.canNotCloseEvent}}</p>

        <template v-if="isRegistrationComplete && membersMustBeNotified">
            <fieldset class="h-36 overflow-y-scroll rounded-sm border">
                <legend class="ml-1 px-1 font-bold">Ausbildungseintrag wird angelegt für</legend>
                <div class="flex flex-wrap gap-2 p-2">
                    <button
                        v-for="{ key, value, isSelected, isDisabled, toggle } in filteredCollection"
                        :key="key"
                        @click="toggle"
                        :disabled="isDisabled.value"
                        class="flex-none cursor-pointer rounded-md px-2 py-0.5 text-sm shadow disabled:pointer-events-none disabled:cursor-not-allowed disabled:hidden "
                        :class="{ 'bg-blue-400 text-white': isSelected.value }">
                        {{ value }}
                    </button>
                </div>
            </fieldset>

            <div class="flex justify-between py-2 text-xs">
                <div class="">
                    <button @click="(_event) => (filter = 'all')" class="underline" :class="{ 'text-blue-500': filter === 'all' }">
                        Alle
                    </button>
                    ,
                    <button @click="(_event) => (filter = 'planning')" class="underline" :class="{ 'text-blue-500': filter === 'planning' }">
                        Eingeplante
                    </button>
                    oder
                    <button @click="(_event) => (filter = 'registration')" class="underline" :class="{ 'text-blue-500': filter === 'registration' }">
                        registrierte
                    </button>
                    Personen
                </div>
                <span>ausgewählt: {{ selected.length }} </span>
            </div>
        </template>

        <template #buttons>
            <button v-if="!isRegistrationComplete" type="button" class="form-button button-contained button-sm" @click="(_event) => finishRegistration()">Okay</button>
            <button v-if="isRegistrationComplete" type="button" class="form-button button-sm" @click="(_event) => cancel()">Abbrechen</button>
            <button v-if="isRegistrationComplete"
                type="button"
                :disabled="!canConfirm"
                @click="confirm"
                class="form-button button-contained button-sm disabled:pointer-events-none disabled:opacity-50">
                Abschließen
            </button>
        </template>
    </UiDialog>
</template>
