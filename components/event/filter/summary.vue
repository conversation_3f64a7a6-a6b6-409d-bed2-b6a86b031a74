<template>
    <div class="flex w-full flex-wrap gap-2 text-sm">
        <UiLabel v-for="({ label, reset }, i) in chunks.visibles" :remove="reset" :key="`label-${i}-${label}`" :title="label">
            {{ label }}
        </UiLabel>

        <UiLabel
            v-if="chunks.rest.length === 1"
            :remove="chunks.rest[0].reset"
            :key="`label-last-${chunks.rest[0].label}`"
            :title="chunks.rest[0].label">
            {{ chunks.rest[0].label }}
        </UiLabel>

        <button v-if="chunks.rest.length > 1" @click="emit('adjust-settings')" class="form-button button-xs">
            Weitere ({{ chunks.rest.length }})
        </button>
    </div>
</template>

<script lang="ts" setup>
    import type { EventFilter } from '~~/composables/event-filter'
    import { useVModel } from '@vueuse/core'

    type Props = {
        modelValue: EventFilter
    }

    type Emit = {
        (e: 'update:modelValue', value: EventFilter): void
        (e: 'adjust-settings', value?: null): void
    }

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const filter = useVModel(props, 'modelValue', emit)

    const {
        type,
        withOpenEventPosts,
        responseStatus,
        withExtendedPermissions,
        responsibleFor,
        assignedToEventPost,
        inChainOfCommand,
        executedBy,
        organisations,
        extendedDescription,
        registratorFor,
        status
    } = useEventFilterFacade(filter)

    const items = computed(() => {
        const items: { label: string; reset: () => void }[] = []

        if (type.value && type.value !== 'ALL') {
            items.push({
                label: type.value === 'EVENT' ? 'Dienste' : 'Ausbildungen',
                reset: () => (type.value = 'ALL')
            })
        }

        if (status.value.includes('WAITING_FOR_APPROVAL')) {
            items.push({
                label: "Noch nicht bestätigte Ereignisse",
                reset: () => {
                    status.value = [...status.value.filter(s=> s !== 'WAITING_FOR_APPROVAL')]
                }
            })
        }

        if (status.value.includes('APPROVED')) {
            items.push({
                label: "Bestätigte Ereignisse",
                reset: () => {
                    status.value = [...status.value.filter(s=> s !== 'APPROVED')]
                }
            })
        }

        if (status.value.includes('CANCELED')) {
            items.push({
                label: "Abgesagte Ereignisse",
                reset: () => {
                    status.value = [...status.value.filter(s=> s !== 'CANCELED')]
                }
            })
        }

        if (status.value.includes('FINISHED')) {
            items.push({
                label: "Abgeschlossene Ereignisse",
                reset: () => {
                    status.value = [...status.value.filter(s=> s !== 'FINISHED')]
                }
            })
        }

        if (!!extendedDescription.value) {
            items.push({
                label: extendedDescription.value,
                reset: () => {
                    extendedDescription.value = null
                }
            })
        }

        if (executedBy.value === 'MY') {
            items.push({
                label: 'Meine Gliederungen',
                reset: () => (executedBy.value = 'ALL')
            })
        }

        if (!!organisations.value) {
            organisations.value.forEach(({ label, id }) => {
                items.push({
                    label: label,
                    reset: () => {
                        organisations.value = organisations.value.filter((existing) => {
                            return existing.id !== id
                        })
                    }
                })
            })
        }

        if (responseStatus.value === 'INVITED' ) {
            items.push({
                label: 'Bin eingeladen',
                reset: () => (responseStatus.value = 'ALL')
            })
        }
        if (responseStatus.value === 'CONFIRMED') {
            items.push({
                label: 'Bin verfügbar',
                reset: () => (responseStatus.value = 'ALL')
            });
        }

        if (responseStatus.value === 'DENIED') {
            items.push({
                label: 'Bin nicht verfügbar',
                reset: () => (responseStatus.value = 'ALL')
            });
        }

        if (responseStatus.value === 'ANSWERED') {
            items.push({
                label: 'Habe mich gemeldet',
                reset: () => (responseStatus.value = 'ALL')
            });
        }

        if (!!withOpenEventPosts.value) {
            items.push({
                label: 'Offene Planstellen',
                reset: () => (withOpenEventPosts.value = false)
            })
        }

        if (!!assignedToEventPost.value) {
            items.push({
                label: 'Bin zugeordnet',
                reset: () => (assignedToEventPost.value = false)
            })
        }

        if (!!registratorFor.value) {
            items.push({
                label: "Darf registrieren",
                reset: () => {
                    registratorFor.value = false
                }
            })
        }

        if (!!withExtendedPermissions.value) {
            items.push({
                label: 'Erweiterte Rechte',
                reset: () => (withExtendedPermissions.value = false)
            })
        }

        if (!!responsibleFor.value) {
            items.push({
                label: 'Bin verantwortlich',
                reset: () => (responsibleFor.value = false)
            })
        }

        if (!!inChainOfCommand.value) {
            items.push({
                label: 'Verknüpfte Dienstwege',
                reset: () => (inChainOfCommand.value = false)
            })
        }

        return items
    })

    const chunks = computed(() => {
        const visibles = items.value.slice(0, 4)
        const rest = items.value.slice(4)
        return {
            visibles,
            rest
        }
    })
</script>
