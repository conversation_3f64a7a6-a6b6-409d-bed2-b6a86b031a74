<template>
  <UiComposer :is-revealed="isRevealed" @cancel="cancel">
    <template #title>Report erstellen</template>

    <form class="mb-4 flex w-full flex-col p-2">
      <div class="flex-1">
        <fieldset class="mb-10 flex flex-col gap-y-4">
          <UiRadioInput v-for="report in ReportType"
            :key="report.value" name="reportType"
            :label="report.label"
            v-model="selectedReport"
            :value="report.value" />
        </fieldset>

        <fieldset v-if="selectedReport === 'PRINT_EVENTS'" class="mb-10 pl-4 flex flex-col gap-y-4">
          <legend class="text-grey-900 mb-6 font-medium -ml-4 block leading-4">Ereignis-Infos</legend>
          <UiRadioInput v-for="print in PrintType"
            :key="print.value"
            name="eventInfo"
            :label="print.label"
            v-model="currentReportOption"
            :value="print.value" />
        </fieldset>

        <fieldset
          v-if="showReportOptions"
          class="mb-10 flex flex-col gap-y-4 pl-4"
        >
          <legend class="text-grey-900 mb-6 font-medium -ml-4 block leading-4">Datengrundlage</legend>
          <UiRadioInput v-for="reportSortOption in ReportSortOption"
          :key="reportSortOption.value" name="dataSource"
          :label="reportSortOption.label"
          v-model="currentReportSortOption"
          :value="reportSortOption.value"
          >
            <span class="text-sx">{{reportSortOption.note}}</span>
          </UiRadioInput>
        </fieldset>

        <fieldset v-if="selectedReport === 'SORTED_EXPORT'" class="mb-4 flex flex-col gap-y-4 pl-4">
          <legend class="text-grey-900 mb-6 font-medium -ml-4 block leading-4">Sortierung</legend>
          <UiRadioInput v-for="registrationOrderedExport in RegistrationOrderedExport"
            :key="registrationOrderedExport.value" name="sortBy"
            :label="registrationOrderedExport.label"
            v-model="currentRegistrationOrderedExport"
            :value="registrationOrderedExport.value" />
        </fieldset>
      </div>

      <p v-if="showReportOptions" class="mb-6 font-medium ">Dateiformat</p>
      <div class="flex gap-2" v-if="showReportOptions" >
        <button
          type="button"
          v-for="fileExt in fileExtensions"
          :key="fileExt"
          class="mb-2 mr-2 rounded-full bg-gray-50 px-2 py-1 text-sm text-gray-500 hover:cursor-pointer hover:bg-gray-200"
          @click="setFileType(fileExt)"
          :class="{ '!bg-gold-100': currentFileExtension === fileExt }"
        >
          {{ fileExt.toUpperCase() }}
      </button>
      </div>
    </form>

    <template #footer>
      <div class="flex w-full justify-end">
        <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
        <button class="form-button button-contained mr-6" :disabled="v$.$invalid" @click="confirm">
          Datei herunterladen
        </button>
      </div>
    </template>
  </UiComposer>
</template>


<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import type { ReportOption } from '~/composables/report'

    const props = defineProps<{
      event: ApiModel<'Event'>
      controller: DialogController<'createPrintableReport'>
    }>()

    const PrintType = [
      {value: 'BASICS', label: 'Stammdaten'},
      {value: 'EMPTY_POSTS', label: 'Gesamte Planung ohne Besetzung'},
      {value: 'POSTS', label: 'Gesamte Planung mit Besetzung'},
      {value: 'TOTAL', label: 'Stammdaten und Planstellen'}
    ]

    const RegistrationOrderedExport = [
      {value: 'PRESENT_FROM', label: 'Anwesend von'},
      {value: 'PRESENT_TO', label: 'Anwesend bis'},
      {value: 'FIRST_NAME', label: 'Vorname'},
      {value: 'LAST_NAME', label: 'Nachname'},
      {value: 'ORGANISATION', label: 'Organisation'}
    ]

    const ReportType = [
      {value: 'PRINT_EVENTS', label: 'Ereignis drucken'},
      {value: 'COMMUNICATION_LIST', label: 'Ereichbarkeiten'},
      {value: 'NAME_LIST', label: 'Namenliste'},
      {value: 'PARTICIPANTS_LIST', label: 'Teilnehmerliste'},
      {value: 'SORTED_EXPORT', label: 'Sortierter Export (Registrierung)'}
    ]

    const ReportSortOption = [
      {value: 'AVAILABLE', label: 'Gemeldet', note: 'Im Report stehen alle Personen, die sich verfügbar oder teilweise verfügbar gemeldet haben'},
      {value: 'ASSIGNED', label: 'Zugeordnet', note: 'Im Report stehen alle Personen, die auf Planstellen eingeplant sind'},
      {value: 'REGISTERED', label: 'Registriert',note: 'Im Report stehen alle Personen, die zum Ereignis registriert wurden'}
    ]

    enum FileFormat {
      PDF = 'pdf',
      XLSX = 'xlsx',
      XLS = 'xls',
      CSV = 'csv'
    }

    const { getDocument } = useCreateReport()
    const { download } = useDownloadBase64()

    const { isRevealed, confirm: _confirm, cancel } = props.controller

    const selectedReport = ref<string | null>(null)
    const currentReportOption = ref<string>(null)
    const currentRegistrationOrderedExport = ref<string>("PRESENT_FROM")
    const currentReportSortOption = ref<string>(null)

    const payload = computed<ReportOption>(() => {
      let fileOption: string | null = null

      if (selectedReport.value === 'PRINT_EVENTS') {
        fileOption = currentReportOption.value
      } else if (selectedReport.value === 'SORTED_EXPORT') {
        fileOption = currentRegistrationOrderedExport.value || currentReportSortOption.value
      } else if (showReportOptions.value) {
        fileOption = currentReportSortOption.value
      }

      return {
        fileType: selectedReport.value,
        fileOption,
        fileExtension: currentFileExtension.value || 'pdf'
      }
    })

    watch(selectedReport, (value) => {
      currentReportOption.value = null
      currentReportSortOption.value = null

      if (value === 'SORTED_EXPORT') {
        currentRegistrationOrderedExport.value = 'PRESENT_FROM'
      } else {
        currentRegistrationOrderedExport.value = ''
      }
    })

    const showReportOptions = computed(() => {
      if(!selectedReport.value) return false
      return (selectedReport.value !== 'PRINT_EVENTS' && selectedReport.value !== 'SORTED_EXPORT')
    })



    const fileExtensions = Object.keys(FileFormat) as Array<keyof typeof FileFormat>;
    const currentFileExtension = ref<string>('PDF')

    function setFileType(fileExt: string) {
        currentFileExtension.value = fileExt
    }

    async function downloadDocument() {
      const document = await getDocument(props.event.id, payload.value)

      if (typeof document === 'string') {
          const fileName = ReportType.find(({ value }) => value === selectedReport.value).label
          download(document, `ereignis-${props.event.id}-${fileName}.${payload.value.fileExtension.toLowerCase()}`)
      }
    }

    const v$ = useVuelidate(
    {
        fileType: { required },
        fileOption: { required },
    },
       payload
    )

    async function confirm() {
        _confirm()
        await downloadDocument()
    }

</script>
