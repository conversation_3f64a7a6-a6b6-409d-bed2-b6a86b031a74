<template>
    <div class="block disabled:pointer-events-none disabled:opacity-40">
        <label class="text-grey-900 mb-1 block text-xs leading-4 empty:hidden" :class="withTooltip ? 'flex items-center' : ''">
            <slot name="label">
                {{ label }}
                <UiTooltip class="z-20" v-if="withTooltip">{{ tooltipMessage }}</UiTooltip>
            </slot>
        </label>
        <slot>The content</slot>
    </div>
</template>

<script lang="ts" setup>
    type Props = {
        label?: string
        withTooltip?: boolean
        tooltipMessage?: string
    }

    const props = defineProps<Props>()
</script>
