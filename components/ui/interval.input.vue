<template>
    <div>
        <slot :start-input-attrs="startAttrs" :end-input-attrs="endAttrs">
            <UiFormField :label="label" v-bind="$attrs">
                <div class="flex flex-col group-[]/row:flex-row group-[]/row:flex-wrap group-[]/row:items-center gap-1">
                    <UiDateTimePicker v-model="start" v-bind="startAttrs" :hideTimeInput="hideTimeInput" @complete="completePicker" />
                    <span class="flex-none text-sm">bis</span>
                    <UiDateTimePicker v-model="end" v-bind="endAttrs" :hideTimeInput="hideTimeInput" ref="nextPickerRef" />
                </div>
            </UiFormField>
        </slot>
    </div>
</template>

<script lang="ts" setup>
    import { useIntervalInput, useNextPickerFocus } from '~~/composables/ui/interval-input'

    const props = withDefaults(
        defineProps<{
            boundaries?: { start: Date; end: Date }
            label?: string
            useMinMax?: boolean
            hideTimeInput?: boolean
        }>(),
        {
            label: 'Zeitraum auswählen',
            useMinMax: true,
            hideTimeInput: false
        }
    )

    const start = defineModel<Date | null>('start')
    const end = defineModel<Date | null>('end')

    const options = computed(() => {
        return {
            boundaries: props.boundaries,
            useMinMax: props.useMinMax
        }
    })
    const { startAttrs, endAttrs } = useIntervalInput(start, end, options)

    const { nextPickerRef, completePicker } = useNextPickerFocus()
</script>
