<template>
    <Disclosure as="div" class="bg-gray-300 p-4" v-slot="{ open }">
        <DisclosureButton
            class="w-full text-gray-900 flex flex-row gap-2 items-center justify-between"
            @click="emitOpenState(open)"
        >
            <slot name="button" />
        </DisclosureButton>
        <DisclosurePanel class="text-gray-500 mt-4">
            <slot />
        </DisclosurePanel>
    </Disclosure>
</template>

<script lang="ts" setup>
    import { Disclosure, DisclosureButton, DisclosurePanel} from '@headlessui/vue'

    const emit = defineEmits<{
        (e: 'update:open', value: boolean): void;
    }>();

    const emitOpenState = (open: boolean) => {
        emit('update:open', open);
    };

</script>
