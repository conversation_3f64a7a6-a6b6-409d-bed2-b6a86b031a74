<template>
    <PopoverRoot :open="openPopover" @update:open="(e) => handleUpdateOpen(e)">
        <PopoverTrigger aria-label="Select hour and minutes" asChild :class="{ 'pointer-events-none': disabled }">
            <div class="relative flex items-center">
                <input
                    type="time"
                    ref="timeInputRef"
                    v-model="modelValue"
                    class="form-input pr-11"
                    :disabled="disabled"
                    @click.stop
                    @keydown="handleInputKeydown"
                    @focus="handleInputFocus" />
                <UiIcon class="absolute right-0 h-8 w-8 pr-3" name="clock" />
            </div>
        </PopoverTrigger>

        <PopoverPortal v-if="!disabled">
            <PopoverContent
                side="bottom"
                class="z-[1000] w-[20rem] md:w-full rounded-sm bg-white p-2 text-sm font-light shadow-xl"
                @openAutoFocus="(e) => e.preventDefault()">
                <div class="grid grid-cols-3">
                    <div class="border-grey-100 col-span-2 border-r md:py-2 md:pl-2 md:pr-4">
                        <p class="mb-4 text-center font-normal">Stunde</p>
                        <div class="grid grid-cols-6 gap-1">
                            <button
                                v-for="(hour, i) in hourOptions"
                                class="h-8 w-8 cursor-pointer rounded-full p-0.5 hover:bg-red-100"
                                :class="`${hour === selectedHour ? 'bg-red-500 font-medium text-white' : ''}`"
                                @click="() => selectHour(hour)"
                                :key="hour + i.toString()">
                                {{ hour }}
                            </button>
                        </div>
                    </div>
                    <div class="md:py-2 md:pl-4 md:pr-2">
                        <p class="mb-4 text-center font-normal">Minuten</p>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-1">
                            <button
                                v-for="(minute, i) in minutesOptions"
                                class="h-8 w-8 cursor-pointer rounded-full p-0.5 hover:bg-red-100"
                                :class="`${minute === selectedMinutes ? 'bg-red-500 font-medium text-white' : ''}`"
                                @click="() => selectMinutes(minute)"
                                :key="minute + i.toString()">
                                {{ minute }}
                            </button>
                        </div>
                    </div>
                </div>
            </PopoverContent>
        </PopoverPortal>
    </PopoverRoot>
</template>

<script setup lang="ts">
    import { useTimePicker } from '~/composables/ui/time-picker'

    defineProps<{
        disabled?: boolean
    }>()

    const modelValue = defineModel<string>()

    const emit = defineEmits<{
        (e: 'time-selected'): void
    }>()

    const isTimeChanged = ref(false)

    const openPopover = ref(false)

    const timeInputRef = ref<HTMLInputElement | null>(null)

    const { hourOptions, minutesOptions, selectedHour, selectedMinutes, selectHour, selectMinutes } = useTimePicker(modelValue)

    defineExpose({
        timeInputRef
    })

    const handleUpdateOpen = (open: boolean) => {
        openPopover.value = open

        if (!open && isTimeChanged.value) {
            emit('time-selected')
            isTimeChanged.value = false
        }
    }

    const handleInputFocus = () => !openPopover.value && (openPopover.value = true)

    const handleInputKeydown = (e) => e.code === 'Space' && e.preventDefault()

    watch(modelValue, (newValue, oldValue) => {
        if (oldValue && newValue !== oldValue) {
            isTimeChanged.value = true
        }
    })
</script>

<style scoped>
    input[type='time']::-webkit-calendar-picker-indicator {
        display: none;
    }
</style>
