<template>
    <div>
        <UiListbox
            v-model="selectedId"
            :disabled="isLoading || $attrs.disabled"
            :multiple="multiple"
            :currentPage="currentPage"
            :withInput="!showSearchInput">
            <template #button>
                <div class="flex items-center gap-x-2">
                    <UiIcon v-if="isLoading" name="refresh" class="h-4 w-4 animate-spin" />
                    <span class="truncate" :class="{ italic: !hasValue || multiple }">{{ buttonText || placeholder }} </span>
                    <IconXCircle
                        v-if="!!selectedId && !multiple"
                        @click.stop="clear"
                        class="listbox-option-cancel text-grey-400 h-5 w-5 hover:cursor-pointer" />
                </div>
            </template>

            <template #header v-if="showSearchInput">
                <div class="mx-3 border-b py-2">
                    <template v-if="withSearchFilters">
                        <slot name="searchFilters"></slot>
                    </template>
                    <ComboboxInput
                        @keydown.left="prev"
                        @keydown.right="next"
                        ref="searchInputRef"
                        autocomplete="off"
                        class="combobox-input"
                        placeholder="Eintrag suchen"
                        @change="searchInput = $event.target.value" />
                </div>
            </template>

            <template #default>
                <UiListboxOption v-for="item of visibleOptions" :key="item.id" :value="item.id">
                    <span v-if="categoryProp" class="text-xs font-semibold">{{ getCategory(item) }} <br /></span>
                    {{ getLabel(item) }}
                </UiListboxOption>
                    <span v-if="searchInput && !searchResults.length && !withSearchFilters" class="ml-3 text-sm text-gray-700">Keine Vorschläge für {{ searchInput }}</span>
                    <div v-else>
                        <span v-if="searchInput && !searchResults.length && !alternativeSearchResults.length" class="ml-3 text-sm text-gray-700">Keine Vorschläge für {{ searchInput }}</span>
                        <span v-if="searchInput && !searchResults.length && alternativeSearchResults.length" class="ml-3 text-sm text-gray-700">{{ alternativeSearchTipNote }}</span>
                    </div>
            </template>

            <template #footer v-if="visibleOptions.length < searchResults.length">
                <div class="mx-3 flex items-center justify-between border-t bg-white py-2 text-xs text-gray-500">
                    <span>{{ searchResults.length }} Einträge</span>
                    <span>Seite {{ currentPage }} von {{ pageCount }}</span>
                    <div class="flex items-center">
                        <button class="form-button button-xs rounded-md" @click="prev" :disabled="isFirstPage">
                            <UiIcon name="arrow-sm-left" class="h-3 w-3" />
                        </button>
                        <button class="form-button button-xs" @click="next" :disabled="isLastPage">
                            <UiIcon name="arrow-sm-right" class="h-3 w-3" />
                        </button>
                    </div>
                </div>
            </template>
        </UiListbox>

        <div v-if="multiple" class="mt-2 flex flex-wrap gap-2 text-sm">
            <span v-for="selected in selectedOptions" :key="selected.id" class="label text-xs">
                {{ getLabel(selected) }}
                <button title="Qualifikation entfernen" @click="remove(selected)" class="label-remove-button" />
            </span>
        </div>
    </div>
</template>

<script setup lang="ts" generic="T extends object & { id: number | string }">
    import { ComboboxInput, Combobox } from '@headlessui/vue'
    import { useFuse } from '@vueuse/integrations/useFuse'
    import { useOffsetPagination } from '@vueuse/core'
    import { useListboxModel } from '~/composables/ui/listbox-model'
    import orderBy from 'just-order-by'

    const props = withDefaults(
        defineProps<{
            options: T[],
            alternativeOptions?: T[],
            labelProp: Extract<keyof T, string>
            categoryProp?: string
            isLoading?: boolean
            placeholder?: string
            withClearButton?: boolean
            visibleOptionsCount?: number
            sortBy?: SortKey
            showSearchInput?: boolean,
            alternativeSearchTipNote?: string,
            withSearchFilters?: boolean
        }>(),
        {
            isLoading: false,
            placeholder: 'Bitte wählen',
            withClearButton: false,
            visibleOptionsCount: 30,
            sortBy: 'label',
            showSearchInput: true,
            searchWithFilters: false,
            alternativeSearchTipNote: 'Keine Vorschläge. Probiere andere Filteroption.',
            alternativeOptions: () => [],
            withSearchFilters: false
        }
    )

    /**
     * Force reactivity of options property
     */
    const options = computed(() => {
        return props.options
    })

    const alternativeOptions = computed(()=>{
        return props.alternativeOptions
    })

    const modelValue = defineModel<T | T[]>()

    const multiple = ref(Array.isArray(modelValue.value))

    /**
     * Local storage of the search input
     */
    const searchInput = ref<string>(null)

    /**
     * The reference to the input field
     * We're observing if it's beeing mounted/unmounted and reset it's value then
     */
    const searchInputRef = ref()
    watchEffect(() => {
        if (!!searchInputRef.value) {
            searchInput.value = null
        }
    })

    type SortKey = 'label' | 'category'

    const selectedSortKey = ref<SortKey>(props.sortBy)

    const sortedOptions = computed(() => {
        return orderBy(options.value, [
            {
                property(option: T) {
                    return option[selectedSortKey.value === 'label' ? props.labelProp : props.categoryProp]
                },
                order: 'asc'
            },
            {
                property(option: T) {
                    return option[selectedSortKey.value === 'label' ? props.categoryProp : props.labelProp]
                },
                order: 'asc'
            }
        ])
    })


    const { results: searchResults } = useFuse<T>(searchInput, sortedOptions, {
        matchAllWhenSearchEmpty: true,
        fuseOptions: {
            keys: [props.labelProp],
            includeScore: true,
            includeMatches: true,
            threshold: 0.4,
            distance: 800,
            findAllMatches: false,
            ignoreLocation: false
        }
    })

    const { results: alternativeSearchResults } = useFuse<T>(searchInput, alternativeOptions , {
        matchAllWhenSearchEmpty: true,
        fuseOptions: {
            keys: [props.labelProp],
            includeScore: true,
            includeMatches: true,
            threshold: 0.4,
            distance: 800,
            findAllMatches: false,
            ignoreLocation: false
        }
    })

    const numberOfSearchResults = computed(() => {
        return searchResults.value.length
    })

    const { currentPage, currentPageSize, pageCount, isFirstPage, isLastPage, prev, next } = useOffsetPagination({
        total: numberOfSearchResults,
        page: 1,
        pageSize: props.visibleOptionsCount
    })

    const visibleOptions = computed(() => {
        const start = (currentPage.value - 1) * currentPageSize.value
        const end = start + currentPageSize.value
        return searchResults.value.map(({ item }) => item).slice(start, end)
    })

    function getLabel(item: T) {
        return item ? item[props.labelProp] : null
    }

    function getCategory(item: T) {
        return item ? item[props.categoryProp] : null
    }

    function isMultipleModelValue(modelValue: Ref<any>): modelValue is Ref<T[]> {
        return !!multiple.value
    }

    function isSingleModelValue(modelValue: Ref<any>): modelValue is Ref<T> {
        return !multiple.value
    }

    const buttonText = computed(() => {
        if (isSingleModelValue(modelValue)) {
            return getLabel(modelValue.value)
        }

        return null
    })

    const hasValue = computed(() => {
        if (isMultipleModelValue(modelValue)) {
            return !!modelValue.value?.length
        }

        if (isSingleModelValue(modelValue)) {
            return !!modelValue.value
        }
    })

    function clear() {
        modelValue.value = null
    }

    /**
     * Wrapper that transforms an option into a model value and vice versa
     */
    const selectedId = useListboxModel(modelValue, options, multiple.value)

    const selectedOptions = computed(() => {
        if (isMultipleModelValue(modelValue)) {
            return modelValue.value.map((item) => item)
        }

        return null
    })

    function remove(item: T) {
        if (isMultipleModelValue(modelValue)) {
            modelValue.value = modelValue.value.filter(({ id }) => id !== item.id)
        }
    }
</script>

<style lang="pcss" scoped>
    .label {
         @apply text-grey-700 bg-grey-100 flex cursor-default select-none items-center whitespace-nowrap rounded-full gap-1 py-1 pl-2 pr-1
     }

     .label-remove-button {
         @apply hover:bg-grey-200 active:bg-grey-300 h-[1.25em] w-[1.25em] -my-[0.25em] inline-block rounded-full text-gray-100;
         @apply icon-x-gray-500 bg-center bg-[length:_1em_1em];
     }
     .combobox-input {
         @apply bg-gray-50 text-base outline-none w-full;
         @apply pr-1 py-1 pl-7 font-light;
         @apply icon-search-gray-500 bg-no-repeat bg-[left_0.25rem_center] bg-[length:1.25rem_1.25rem];
         @apply focus:icon-search-blue-500
     }

    .listbox-button {
        @apply form-input w-full cursor-default leading-tight pr-7;
        @apply icon-selector-gray-400 bg-no-repeat bg-[right_0.125rem_center] bg-[length:1.25rem_1.25rem];

        .group\/listbox  &[data-headlessui-state~=open] {
            @apply ui-open:icon-selector-blue-500
        }
    }
</style>
