import { useMutation, useQueryClient, type UseMutationOptions, QueryFilters, QueryKey } from '@tanstack/vue-query'
import { MaybeRef } from '@vueuse/core'
import { pick } from 'lodash-es'
import SignUpStatus from '~~/enums/event-signing-up-status'
import { ApiSchema } from '~~/schema/factory'

type ListResponse<T = any> = {
    totalItems: number
    offset: number
    limit: number
    items: T[]
}

// We have to translate the signing up status into one of these values before
// sending create or update requests
export const signingUpStatusMap = {
    [SignUpStatus.AVAILABLE]: 'AVAILABLE',
    [SignUpStatus.PARTIAL]: 'AVAILABLE',
    [SignUpStatus.UNAVAILABLE]: 'NOT_AVAILABLE'
} as const

/**
 * CREATE EVENT
 */

type EventMutationData = {
    data: ReturnType<ApiModel<'Event'>['toJSON']>
    signal?: AbortSignal
}

export const useCreateEvent = function (noQueryValidation = false) {
    const queryClient = useQueryClient()

    const { mutateAsync: createEvent } = useMutation({
        mutationFn: async ({ data, signal }: EventMutationData) => {
            const event = useModelFactory('Event').fromJson(
                await $fetch<object>(`/api/events/`, {
                    body: data,
                    method: 'post',
                    signal
                })
            )
            return event
        },

        onSettled: async () => {
            if (noQueryValidation) return
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })
        }
    })

    return {
        createEvent
    }
}


export const useEventMutations = function (input: MaybeRef<ApiModel<'Event'>>) {
    const queryClient = useQueryClient()

    const { $user } = useNuxtApp()

    const eventId = computed(() => {
        return unref(input).id
    })

    /**
     * Helpers
     */
    function setQueriesData<T extends any>(queryKey: QueryKey, updater: (previous: T) => T, exact = true) {
        const modified = queryClient
            .getQueriesData<T>({
                queryKey,
                type: 'active',
                exact
            })
            .map(([key, previous]): [QueryKey, T] => {
                queryClient.setQueryData(key, updater(previous))
                return [key, previous]
            })

        return modified
    }

    /**
     * EVENT POSTS
     */

    /**
     * Create event post
     */
    const { mutateAsync: createEventPost } = useMutation({
        mutationFn: async (eventPost: ApiModel<'EventPost'>) => {
            return useModelFactory('EventPost').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/event-posts`, {
                    body: eventPost,
                    method: 'post'
                })
            )
        },

        onMutate(eventPost) {
            return {
                modified: setQueriesData<ApiModel<'EventPost'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                    // The updater: Attach given signing up to the list
                    (previous) => [...previous, eventPost]
                )
            }
        },

        onSuccess(eventPost, { _uuid: uuid }) {
            return {
                modified: setQueriesData<ApiModel<'EventPost'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                    // The updater: Set `_uuid` of created signing up to the respective value of scoffolded entry.
                    // ...then replace the scaffolded entry with the created one.
                    (previous) => {
                        eventPost._uuid = uuid
                        return [...previous.filter(({ _uuid }) => _uuid !== uuid), eventPost]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                refetchType: 'none',
                exact: true
            })

            // We have to reload events to update list on /events
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

        }
    })

    /**
     * Update event post
     *
     * @todo: Rename to `updateEventPost`
     */
    const { mutateAsync: updateEventPost } = useMutation({
        mutationFn: async (eventPost: ApiModel<'EventPost'>) => {
            return useModelFactory('EventPost').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/event-posts/${eventPost.id}`, {
                    body: eventPost,
                    method: 'PATCH'
                })
            )
        },

        onMutate(eventPost) {
            return {
                modified: setQueriesData<ApiModel<'EventPost'>[]>(
                    //....
                    queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                    (previous) => [...previous.filter(({ id }) => id !== eventPost.id), eventPost]
                )
            }
        },

        onSuccess(eventPost) {
            return {
                modified: setQueriesData<ApiModel<'EventPost'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                    // Replace the old event post with the updated version
                    (previous) => [...previous.filter(({ id }) => id !== eventPost.id), eventPost]
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                refetchType: 'none',
                exact: true
            })

            // We have to reload signing ups
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload resource settings
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload conflicts to check if typ/art/modul
            // to check if there is no conflict with resource add to event
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.conflicts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload events to update list on /events
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

        }
    })

    /**
     * Remove event post
     */
    const { mutateAsync: removeEventPost } = useMutation({
        mutationFn: (eventPost: ApiModel<'EventPost'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/event-posts/${eventPost.id}`, {
                method: 'delete'
            })
        },

        onMutate(eventPost) {
            return {
                modified: setQueriesData<ApiModel<'EventPost'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                    // The updater: Return everything but the given event post
                    (previous) => [...previous.filter(({ id }) => id !== eventPost.id)]
                )
            }
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                refetchType: 'none',
                exact: true
            })

            // We have to reload signing ups
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload resource settings
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                refetchType: 'active',
                exact: true
            })

            // Reload conflicts
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.conflicts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload events to update list on /events
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })
        }
    })

    /**
     * SIGNING UPS
     *
     * - create
     * - update
     * - delete
     */

    // We need this helper to extract a known status value from signing up
    const { retrieveStatus: retrieveSigningUpStatus } = useSigningUpStates()


    // Some special treatment is needed if the changed signing up belongs to the current user
    function isMySigningUp(signinUp: ApiModel<'SigningUp'>) {
        return signinUp.resource.member?.id === $user.basicData.id
    }

    /**
     * Create new signing ups
     */
    const { mutateAsync: createSigningUp } = useMutation({
        mutationFn: async (signingUp: ApiModel<'SigningUp'>) => {
            return useModelFactory('SigningUp').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/signing-ups`, {
                    method: 'post',
                    body: {
                        status: signingUpStatusMap[retrieveSigningUpStatus(signingUp)],
                        resource: signingUp.resource,
                        periods: signingUp.periods
                    }
                })
            ) as ApiModel<'SigningUp'>
        },

        onMutate(signingUp) {
            const modfiedSigningUpQueries = setQueriesData<ApiModel<'SigningUp'>[]>(
                // The query key
                queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                // The updater: Attach given signing up to the list
                (previous) => [...previous, signingUp]
            )

            const modifiedEventDetailQueries = isMySigningUp(signingUp)
                ? setQueriesData<ApiModel<'Event'>>(
                    // The active event details query
                    queries.events.detail(eventId.value).queryKey,
                    // Update the event with the signing up
                    (previous) =>
                        useModelFactory('Event').create({
                            ...previous,
                            mySigningUp: signingUp
                        })
                )
                : []

            const modifiedEventListQueries = isMySigningUp(signingUp)
                ? setQueriesData<ListResponse<ApiModel<'Event'>>>(
                    // The active event list query
                    queries.events.list._def,

                    (previous) => {
                        // Create a copy of contained items
                        // Update the event that has been modified
                        const items = previous.items.map((event) => {
                            if (event.id === eventId.value) {
                                return useModelFactory('Event').create({
                                    ...event,
                                    mySigningUp: signingUp
                                })
                            }
                            return event
                        })

                        return {
                            ...previous,
                            items
                        }
                    },
                    // No exact search
                    false
                )
                : []

            return {
                modified: [...modfiedSigningUpQueries, ...modifiedEventDetailQueries, ...modifiedEventListQueries]
            }
        },

        onSuccess(signingUp, { _uuid: uuid }) {
            const modfiedSigningUpQueries = setQueriesData<ApiModel<'SigningUp'>[]>(
                // The query key
                queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                // Set `_uuid` of created signing up to the respective value of scaffolded entry.
                // ...then replace the scaffolded entry with the created one.
                (previous) => {
                    signingUp._uuid = uuid
                    return [...previous.filter(({ _uuid }) => _uuid !== uuid), signingUp]
                }
            )

            const modifiedEventDetailQueries = isMySigningUp(signingUp)
                ? setQueriesData<ApiModel<'Event'>>(
                    // The active event details query
                    queries.events.detail(eventId.value).queryKey,
                    // Update the event with the signing up
                    (previous) =>
                        useModelFactory('Event').create({
                            ...previous,
                            mySigningUp: signingUp
                        })
                )
                : []

            const modifiedEventListQueries = isMySigningUp(signingUp)
                ? setQueriesData<ListResponse<ApiModel<'Event'>>>(
                    // The active event list query
                    queries.events.list._def,

                    (previous) => {
                        // Create a copy of contained items
                        // Update the event that has been modified
                        const items = previous.items.map((event) => {
                            if (event.id === eventId.value) {
                                return useModelFactory('Event').create({
                                    ...event,
                                    mySigningUp: signingUp
                                })
                            }
                            return event
                        })

                        return {
                            ...previous,
                            items
                        }
                    },
                    // No exact search
                    false
                )
                : []

            return {
                modified: [...modfiedSigningUpQueries, ...modifiedEventDetailQueries, ...modifiedEventListQueries]
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled(_, __) {
            // Invalidate signing ups but don't refetch it immediatly
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'none',
                exact: true
            })

            // Invalidate list because we probably changed "mySigningUp"
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'none',
                exact: false
            })

            // Invalidate event because we probably changed "mySigningUp"
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Update signing up
     *
     */
    const { mutateAsync: updateSigningUp } = useMutation({
        mutationFn: async (signingUp: ApiModel<'SigningUp'>) => {
            return useModelFactory('SigningUp').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/signing-ups/${signingUp.id}`, {
                    method: 'put',
                    body: {
                        status: signingUpStatusMap[retrieveSigningUpStatus(signingUp)],
                        resource: signingUp.resource,
                        periods: signingUp.periods
                    }
                })
            ) as ApiModel<'SigningUp'>
        },

        onMutate(signingUp) {
            const modfiedSigningUpQueries = setQueriesData<ApiModel<'SigningUp'>[]>(
                //....
                queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                (previous) => [...previous.filter(({ id }) => id !== signingUp.id), signingUp]
            )

            const modifiedEventDetailQueries = isMySigningUp(signingUp)
                ? setQueriesData<ApiModel<'Event'>>(
                    // The active event details query
                    queries.events.detail(eventId.value).queryKey,
                    // Update the event with the signing up
                    (previous) =>
                        useModelFactory('Event').create({
                            ...previous,
                            mySigningUp: signingUp
                        })
                )
                : []

            return {
                modified: [...modfiedSigningUpQueries, ...modifiedEventDetailQueries]
            }
        },

        onSuccess(signingUp) {
            const modfiedSigningUpQueries = setQueriesData<ApiModel<'SigningUp'>[]>(
                // The query key
                queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                // Replace the old siging uo with the updated version
                (previous) => [...previous.filter(({ id }) => id !== signingUp.id), signingUp]
            )

            const modifiedEventDetailQueries = isMySigningUp(signingUp)
                ? setQueriesData<ApiModel<'Event'>>(
                    // The active event details query
                    queries.events.detail(eventId.value).queryKey,
                    // Update the event with the signing up
                    (previous) =>
                        useModelFactory('Event').create({
                            ...previous,
                            mySigningUp: signingUp
                        })
                )
                : []

            return {
                modified: [...modfiedSigningUpQueries, ...modifiedEventDetailQueries]
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled() {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'none',
                exact: true
            })

            // Invalidate event because we probably changed "mySigingUp"
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'none',
                exact: true
            })

            // Reload list because we probably changed "mySigingUp"
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })
        }
    })

    /**
     * Remove signing up
     *
     */
    const { mutateAsync: removeSigningUp } = useMutation({
        mutationFn: (signingUp: ApiModel<'SigningUp'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/signing-ups/${signingUp.id}`, {
                method: 'delete',
                body: { ...signingUp.resource }
            })
        },

        onMutate(signingUp) {
            return {
                modified: setQueriesData<ApiModel<'SigningUp'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                    // The updater: Return everything but the given signing up
                    (previous) => [...previous.filter(({ id }) => id !== signingUp.id)]
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled() {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'none',
                exact: false
            })
            // Invalidate event
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * RESOURCE SETTINGS
     *
     * - create
     * - update
     * - delete
     */

    /**
     * Create a new resource setting
     */
    const { mutateAsync: createResourceSetting } = useMutation({
        mutationFn: async (resourceSetting: ApiModel<'ResourceSetting'>) => {
            return useModelFactory('ResourceSetting').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/resource-settings`, {
                    body: {
                        eventPostId: resourceSetting.eventPost.id,
                        eventSigningUpId: resourceSetting.eventSigningUp.id,
                        periods: resourceSetting.periods
                    },
                    method: 'post'
                })
            )
        },

        onMutate(resourceSetting) {
            return {
                modified: setQueriesData<ApiModel<'ResourceSetting'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                    (previous) => [...previous, resourceSetting]
                )
            }
        },

        onSuccess(resourceSetting, { _uuid: uuid }) {
            return {
                modified: setQueriesData<ApiModel<'ResourceSetting'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                    // The updater: Set `_uuid` of created resource setting to the respective value of scaffolded entry.
                    // ...then replace the scaffolded entry with the created one.
                    (previous) => {
                        resourceSetting._uuid = uuid
                        return [...previous.filter(({ _uuid }) => _uuid !== uuid), resourceSetting]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                refetchType: 'none',
                exact: true
            })

            // We have to reload signing ups
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload event posts
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload conflicts
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.conflicts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload events to update list on /events
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

        }
    })

    /**
     * Update Resource setting
     *
     * @todo: Rename to `updateResourceSetting`
     */
    const { mutateAsync: updateResourceSetting } = useMutation({
        mutationFn: async (resourceSetting: ApiModel<'ResourceSetting'>) => {
            return useModelFactory('ResourceSetting').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/resource-settings/${resourceSetting.id}`, {
                    method: 'PATCH',
                    body: {
                        eventPostId: resourceSetting.eventPost.id,
                        eventSigningUpId: resourceSetting.eventSigningUp.id,
                        periods: resourceSetting.periods
                    }
                })
            )
        },

        onMutate(resourceSetting) {
            return {
                modified: setQueriesData<ApiModel<'ResourceSetting'>[]>(
                    //....
                    queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                    (previous) => [...previous.filter(({ id }) => id !== resourceSetting.id), resourceSetting]
                )
            }
        },

        onSuccess(resourceSetting) {
            return {
                modified: setQueriesData<ApiModel<'ResourceSetting'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                    // Replace the old resource setting with the updated version
                    (previous) => [...previous.filter(({ id }) => id !== resourceSetting.id), resourceSetting]
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload signing ups
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload event posts
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload conflicts
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.conflicts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload events to update list on /events
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

        }
    })

    /**
     * Remove resource setting
     */
    const { mutateAsync: removeResourceSetting } = useMutation({
        mutationFn: (resourceSetting: ApiModel<'ResourceSetting'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/resource-settings/${resourceSetting.id}`, {
                method: 'delete'
            })
        },

        onMutate(resourceSetting) {
            return {
                modified: setQueriesData<ApiModel<'ResourceSetting'>[]>(
                    // The query key
                    queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                    // The updater: Return everything but the given signing up
                    (previous) => [...previous.filter(({ id }) => id !== resourceSetting.id)]
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.resourceSettings().queryKey,
                refetchType: 'none',
                exact: true
            })

            // We have to reload signing ups
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload event posts
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.eventPosts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload conflicts
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.conflicts().queryKey,
                refetchType: 'active',
                exact: true
            })

            // We have to reload events to update list on /events
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

        }
    })

    /**
     * Registrations
     *
     * - create
     * - update
     * - delete
     * - replace (transfer from planning or signing ups)
     */

    /**
     * Create a registration entry for a person (member)
     */
    const { mutateAsync: createRegistration } = useMutation({
        mutationFn: async (registration: ApiModel<'EventRegistration'>) => {
            return useModelFactory('EventRegistration').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/registrations`, {
                    body: registration,
                    method: 'post'
                })
            )
        },

        onMutate(registration) {
            return {
                modified: setQueriesData<ApiModel<'EventRegistration'>[]>(
                    queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                    (previous) => {
                        return [...previous, registration]
                    }
                )
            }
        },

        onSuccess(registration, { _uuid: uuid }) {
            return {
                modified: setQueriesData<ApiModel<'EventRegistration'>[]>(
                    queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                    // The updater: Set `_uuid` of created registration to the value of scoffolded entry.
                    // ...then replace the scaffolded entry with the created one.
                    (previous) => {
                        registration._uuid = uuid
                        return [...previous.filter(({ _uuid }) => _uuid !== uuid), registration]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Update the start and/or end time of a registered person
     */
    const { mutateAsync: updateRegistration } = useMutation({
        mutationFn: async (registration: ApiModel<'EventRegistration'>) => {
            return useModelFactory('EventRegistration').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/registrations/${registration.id}`, {
                    body: registration,
                    method: 'put'
                })
            )
        },

        onMutate(registration) {
            return {
                modified: setQueriesData<ApiModel<'EventRegistration'>[]>(
                    queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                    (previous) => {
                        return [...previous.filter(({ id }) => id !== registration.id), registration]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Remove a person from the list of registered persons
     */
    const { mutateAsync: removeRegistration } = useMutation({
        mutationFn: (registration: ApiModel<'EventRegistration'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/registrations/${registration.id}`, {
                method: 'delete'
            })
        },

        onMutate(registration) {
            return {
                modified: setQueriesData<ApiModel<'EventRegistration'>[]>(
                    queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                    (previous) => {
                        return [...previous.filter(({ id }) => id !== registration.id)]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Replace the whole list with the server generated list of registrations
     */
    const { mutateAsync: replaceRegistrations } = useMutation({
        mutationFn: async (type: 'PLANNING' | 'SIGNING') => {
            return useModelFactory('EventRegistration').fromJson(
                await $fetch<object[]>(`/api/events/${eventId.value}/registrations/_transfer`, {
                    query: { type },
                    method: 'post'
                })
            )
        },

        onMutate() {
            return {
                modified: setQueriesData<ApiModel<'EventRegistration'>[]>(
                    queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                    (_previous) => [] // The list is empty for a moment
                )
            }
        },

        onSuccess(registrations) {
            return {
                modified: setQueriesData<ApiModel<'EventRegistration'>[]>(
                    queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                    (_previous) => registrations // Replace list with the one we just recieved
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.registrations().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Registrators a.k.a Legitimate persons
     *
     * - create
     * - update
     */

    /**
     * Create a registration entry for a person (a.k. a. MemberData)
     */
    const { mutateAsync: createRegistrator } = useMutation({
        mutationFn: async (registrator: ApiModel<'MemberData'>) => {
            return useModelFactory('MemberData').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/registrators`, {
                    body: registrator,
                    method: 'post'
                })
            )
        },

        onMutate(registrator) {
            return {
                modified: setQueriesData<ApiModel<'MemberData'>[]>(queries.events.detail(eventId.value)._ctx.registrators().queryKey, (previous) => {
                    return [...previous, registrator]
                })
            }
        },

        onSuccess(registrator, { _uuid: uuid }) {
            return {
                modified: setQueriesData<ApiModel<'MemberData'>[]>(
                    queries.events.detail(eventId.value)._ctx.registrators().queryKey,
                    // The updater: Set `_uuid` of created registrator to the value of scoffolded entry.
                    // ...then replace the scaffolded entry with the created one.
                    (previous) => {
                        registrator._uuid = uuid
                        return [...previous.filter(({ _uuid }) => _uuid !== uuid), registrator]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.registrators().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Remove a person from the list of legitimated persons
     */
    const { mutateAsync: removeRegistrator } = useMutation({
        mutationFn: (registrator: ApiModel<'MemberData'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/registrators/${registrator.id}`, {
                method: 'delete'
            })
        },

        onSuccess(_, registrator) {
            return {
                modified: setQueriesData<ApiModel<'MemberData'>[]>(queries.events.detail(eventId.value)._ctx.registrators().queryKey, (previous) => {

                    return [...previous.filter(({ id }) => id !== registrator.id)]
                })
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.registrators().queryKey,
                refetchType: 'none'
            })
        }
    })

    /**
     * Event Responsibles
     *
     * - create
     * - remove
     */

    /**
     * Create a responsible person
     */
    const { mutateAsync: createResponsible } = useMutation({
        mutationFn: async (responsibleMember: ApiModel<'MemberData'>) => {
            return useModelFactory('MemberData').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/responsibles`, {
                    body: responsibleMember,
                    method: 'post'
                })
            )
        },

        onMutate(responsibleMember) {
            return {
                modified: setQueriesData<ApiModel<'MemberData'>[]>(
                    //...
                    queries.events.detail(eventId.value)._ctx.responsibles().queryKey,
                    (previous) => {
                        return [...previous, responsibleMember]
                    }
                )
            }
        },

        onSuccess(responsibleMember, { _uuid: uuid }) {
            return {
                modified: setQueriesData<ApiModel<'MemberData'>[]>(
                    queries.events.detail(eventId.value)._ctx.responsibles().queryKey,
                    // The updater: Set `_uuid` of created registrator to the value of scoffolded entry.
                    // ...then replace the scaffolded entry with the created one.
                    (previous) => {
                        responsibleMember._uuid = uuid
                        return [...previous.filter(({ _uuid }) => _uuid !== uuid), responsibleMember]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.responsibles().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Remove a responsible person from the event
     */
    const { mutateAsync: removeResponsible } = useMutation({
        mutationFn: (responsibleMember: ApiModel<'MemberData'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/responsibles/${responsibleMember.id}`, {
                method: 'delete'
            })
        },

        onMutate(responsibleMember) {
            return {
                modified: setQueriesData<ApiModel<'MemberData'>[]>(
                    //...
                    queries.events.detail(eventId.value)._ctx.responsibles().queryKey,
                    (previous) => {
                        return [...previous.filter(({ id }) => id !== responsibleMember.id)]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.responsibles().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Event Locations
     *
     * - create
     * - update
     * - remove
     */

    /**
     * Create an event location
     */
    const { mutateAsync: createLocation } = useMutation({
        mutationFn: async (address: ApiModel<'EventAddress'>) => {
            return useModelFactory('EventAddress').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/locations`, {
                    body: address,
                    method: 'post'
                })
            )
        },

        onMutate(address) {
            return {
                modified: setQueriesData<ApiModel<'EventAddress'>[]>(
                    //...
                    queries.events.detail(eventId.value)._ctx.locations().queryKey,
                    (previous) => {
                        return [...previous, address]
                    }
                )
            }
        },

        onSuccess(address, { _uuid: uuid }) {
            return {
                modified: setQueriesData<ApiModel<'EventAddress'>[]>(
                    queries.events.detail(eventId.value)._ctx.locations().queryKey,
                    // The updater: Set `_uuid` of created event addresss to the value of scoffolded entry.
                    // ...then replace the scaffolded entry with the created one.
                    (previous) => {
                        address._uuid = uuid
                        return [...previous.filter(({ _uuid }) => _uuid !== uuid), address]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.locations().queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Update an event location
     */
    const { mutateAsync: updateLocation } = useMutation({
        mutationFn: async (address: ApiModel<'EventAddress'>) => {
            return useModelFactory('EventAddress').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/locations/${address.id}`, {
                    body: address,
                    method: 'PATCH'
                })
            )
        },

        onMutate(address) {
            return {
                modified: setQueriesData<ApiModel<'EventAddress'>[]>(
                    //...
                    queries.events.detail(eventId.value)._ctx.locations().queryKey,
                    (previous) => {
                        return [...previous.filter(({ id }) => id !== address.id), address]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.locations().queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Remove a location from the event
     */
    const { mutateAsync: removeLocation } = useMutation({
        mutationFn: (address: ApiModel<'EventAddress'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/locations/${address.id}`, {
                method: 'delete'
            })
        },

        onMutate(address) {
            return {
                modified: setQueriesData<ApiModel<'EventAddress'>[]>(
                    //...
                    queries.events.detail(eventId.value)._ctx.locations().queryKey,
                    (previous) => {
                        return [...previous.filter(({ id }) => id !== address.id)]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.locations().queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Update basic event data
     */

    const basicEventProps = [
        'organisation',
        'dateFrom',
        'dateUpTo',
        'category',
        'apprenticeshipType',
        'description',
        'extendedDescription',
        'number',
        'remark',
        'operation',
        'internalEventRemark',
        'publicEventRemark',
        'hasNightQuarters',
        'requiredMaleQuarters',
        'requiredFemaleQuarters',
        'requiredDiverseQuarters',
        'requiredNoinfoQuarters',
        'withCosts',
        'reimbursementOfCosts',
        'meetingpoint',
        'meetingpointTime'
    ] as const

    type BasicEventPropery = (typeof basicEventProps)[number]

    const { mutateAsync: updateBasicData } = useMutation({
        mutationFn: async (event: ApiModel<'Event'>) => {
            return useModelFactory('Event').fromJson(
                await $fetch<object>(`/api/events/${event.id}`, {
                    body: pick(event, basicEventProps),
                    method: 'PATCH'
                })
            )
        },

        onMutate(changedEvent) {
            const modiefiedDetailQueries = setQueriesData<ApiModel<'Event'>>(
                //..
                queries.events.detail(eventId.value).queryKey,
                (previous) => {
                    return useModelFactory('Event').create({
                        ...previous,
                        ...pick(changedEvent, basicEventProps)
                    })
                }
            )

            const modifiedEventListQueries = setQueriesData<ListResponse<ApiModel<'Event'>>>(
                // The active event list query
                queries.events.list._def,

                (previous) => {
                    // Update the event that has been modified
                    const items = previous.items.map((event) => {
                        if (event.id === eventId.value) {
                            return useModelFactory('Event').create({
                                ...event,
                                ...pick(changedEvent, basicEventProps)
                            })
                        }
                        return event
                    })

                    return {
                        ...previous,
                        items
                    }
                },
                // No exact search
                false
            )

            return {
                modified: [...modiefiedDetailQueries, ...modifiedEventListQueries]
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async (_data, _variables, { id }) => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(id).queryKey,
                refetchType: 'none',
                exact: true
            })

            // Invalidate lists and reload active list (because the changes could have an effect on the current filter)
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })
        }
    })

    /**
     * Update organizer of the given event
     *
     */
    const { mutateAsync: updateOrganizer } = useMutation({
        mutationFn: async (organizer: ApiModel<'ComplexAddressContact'>) => {
            return useModelFactory('ComplexAddressContact').fromJson(
                //...
                await $fetch<object>(`/api/events/${eventId.value}/organizer`, { body: organizer, method: 'PUT' })
            )
        },

        onMutate(organizer) {
            return {
                modified: setQueriesData<ApiModel<'ComplexAddressContact'>>(
                    queries.events.detail(eventId.value)._ctx.organizer().queryKey,
                    (_previous) => organizer
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async (_data, _variables, __) => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.organizer().queryKey,
                refetchType: 'none',
                exact: false
            })
        }
    })

    /**
     * Update dresscodes for the given event
     */
    const { mutateAsync: updateDresscodes } = useMutation({
        mutationFn: async (dresscodes: ApiModel<'CodeEntry'>[]) => {
            return useModelFactory('CodeEntry').fromJson(
                //...
                await $fetch<object[]>(`/api/events/${eventId.value}/dresscodes`, { body: dresscodes, method: 'PUT' })
            )
        },

        onMutate(dresscodes) {
            return {
                modified: setQueriesData<ApiModel<'CodeEntry'>[]>(
                    queries.events.detail(eventId.value)._ctx.dresscodes().queryKey,
                    (_previous) => dresscodes
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async (_data, _variables, __) => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.dresscodes().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Update catering settings for the given event
     */
    const { mutateAsync: updateCaterings } = useMutation({
        mutationFn: async (caterings: ApiModel<'CodeEntry'>[]) => {
            return useModelFactory('CodeEntry').fromJson(
                //...
                await $fetch<object[]>(`/api/events/${eventId.value}/caterings`, { body: caterings, method: 'PUT' })
            )
        },

        onMutate(caterings) {
            return {
                modified: setQueriesData<ApiModel<'CodeEntry'>[]>(
                    queries.events.detail(eventId.value)._ctx.caterings().queryKey,
                    (_previous) => caterings
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async (_data, _variables, __) => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.caterings().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Add internal publication of an event
     */

    const { mutateAsync: addInternalPublication } = useMutation({
        mutationFn: (internalPublication: ApiModel<'InternalPublication'>) => {
            return $fetch<ApiModel<'InternalPublication'>>(`/api/events/${eventId.value}/internal-publication`, {
                body: internalPublication,
                method: 'POST'
            })
        },

        onMutate() {
            return {
                modified: setQueriesData<ApiModel<'InternalPublication'>>(
                    queries.events.detail(eventId.value)._ctx.internalPublication().queryKey,
                    (_previous) => _previous
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.internalPublication().queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Update the publication status of an event
     */
    const { mutateAsync: updateInternalPublication } = useMutation({
        mutationFn: (internalPublication: ApiModel<'InternalPublication'>) => {
            return $fetch<ApiModel<'InternalPublication'>>(`/api/events/${eventId.value}/internal-publication`, {
                body: internalPublication,
                method: 'PATCH'
            })
        },

        onMutate(internalPublication) {
            return {
                modified: setQueriesData<ApiModel<'InternalPublication'>>(
                    queries.events.detail(eventId.value)._ctx.internalPublication().queryKey,
                    (_previous) => internalPublication
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.internalPublication().queryKey,
                refetchType: 'all',
                exact: true
            })
        }
    })

    /**
     * remove publication of an event
     */

    const { mutateAsync: removeInternalPublication } = useMutation({
        mutationFn: (internalPublication: ApiModel<'InternalPublication'>) => {
            return $fetch<ApiModel<'InternalPublication'>>(`/api/events/${eventId.value}/internal-publication`, {
                body: internalPublication,
                method: 'DELETE'
            })
        },

        onMutate(internalPublication) {
            return {
                modified: setQueriesData<ApiModel<'InternalPublication'>>(
                    queries.events.detail(eventId.value)._ctx.internalPublication().queryKey,
                    (_previous) => internalPublication
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.internalPublication().queryKey,
                refetchType: 'all',
                exact: true
            })
        }
    })

    /**
     * Documents
     *
     * - create
     * - update
     * - remove
     */

    /**
     * Create a document and attach it to the event
     */
    const { mutateAsync: createDocument } = useMutation({
        mutationFn: async (documentData: FormData) => {
            return useModelFactory('EventDocumentMeta').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/documents`, {
                    body: documentData,
                    method: 'POST'
                })
            )
        },

        // onMutate(address) {
        //    It would be possible to extract an document-meta object from formdata.
        // },

        onSuccess(document) {
            return {
                modified: setQueriesData<ApiModel<'EventDocumentMeta'>[]>(
                    queries.events.detail(eventId.value)._ctx.documents().queryKey,
                    // Since we don't modify onMutate we just add the recieved document to the list
                    (previous) => {
                        return [...previous, document]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            // modified.forEach(([key, data]) => {
            //     queryClient.setQueryData(key, data)
            // })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.documents().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Update a document
     */
    const { mutateAsync: updateDocument } = useMutation({
        mutationFn: async (document: ApiModel<'EventDocumentMeta'>) => {
            return useModelFactory('EventDocumentMeta').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/documents/${document.id}`, {
                    body: document,
                    method: 'PATCH'
                })
            )
        },

        onMutate(document) {
            return {
                modified: setQueriesData<ApiModel<'EventDocumentMeta'>[]>(
                    //...
                    queries.events.detail(eventId.value)._ctx.documents().queryKey,
                    (previous) => {
                        return [...previous.filter(({ id }) => id !== document.id), document]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.documents().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Remove a document from the event
     */
    const { mutateAsync: removeDocument } = useMutation({
        mutationFn: (document: ApiModel<'EventDocumentMeta'>) => {
            return $fetch<any>(`/api/events/${eventId.value}/documents/${document.id}`, {
                method: 'delete'
            })
        },

        onMutate(document) {
            return {
                modified: setQueriesData<ApiModel<'EventDocumentMeta'>[]>(
                    //...
                    queries.events.detail(eventId.value)._ctx.documents().queryKey,
                    (previous) => {
                        return [...previous.filter(({ id }) => id !== document.id)]
                    }
                )
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.documents().queryKey,
                refetchType: 'none',
                exact: true
            })
        }
    })

    /**
     * Event Actions
     *
     * - cancel
     * - reactivate
     * - close
     * - open
     * - remove
     */

    /**
     * Cancel event
     */
    const { mutateAsync: cancelEvent } = useMutation({
        mutationFn: (cancelReason: ApiSchema<'EventCancelReason'>) => {
            return $fetch<ApiModel<'Event'>>(`/api/events/${eventId.value}/cancel-event`, {
                body: cancelReason,
                method: 'post'
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Reactivate event
     */
    const { mutateAsync: reactivateEvent } = useMutation({
        mutationFn: (_action?: string) => {
            return $fetch<ApiModel<'Event'>>(`/api/events/${eventId.value}/reactivate-event`, {
                method: 'get'
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Close event
     */
    const { mutateAsync: closeEvent } = useMutation({
        mutationFn: (members: ApiModel<'MemberData'>[]) => {
            return $fetch<ApiModel<'Event'>>(`/api/events/${eventId.value}/close-event`, {
                body: members,
                method: 'post'
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Reopen event
     */
    const { mutateAsync: openEvent } = useMutation({
        mutationFn: (_action?: string) => {
            return $fetch<ApiModel<'Event'>>(`/api/events/${eventId.value}/open-event`, {
                method: 'get'
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })

            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'active',
                exact: true
            })
        }
    })

    /**
     * Remove event
     */
    const { mutateAsync: removeEvent } = useMutation({
        mutationFn: (event: ApiModel<'Event'>) => {
            return $fetch<void>(`/api/events/${eventId.value}`, {
                method: 'delete'
            })
        },

        onSuccess: () => {
            queryClient.removeQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                exact: false
            })
        },

        onSettled: async () => {
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })
        }
    })

    /**
     * Update external material in event
     *
     */
    const { mutateAsync: updateExternalMaterial } = useMutation({
        mutationFn: async (signingUp: ApiModel<'SigningUp'>) => {
            return useModelFactory('SigningUp').fromJson(
                await $fetch<object>(`/api/events/${eventId.value}/signing-ups/external-technic-article/${signingUp.resource.externalArticle.id}`, {
                    method: 'PATCH',
                    body: signingUp.resource.externalArticle
                })
            )
        },

        onMutate: (signingUp) => {
            const modfiedSigningUpQueries = setQueriesData<ApiModel<'SigningUp'>[]>(
                //....
                queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                (previous) => {
                    return [
                        ...previous.filter((signingUpItem) => {
                            if (signingUpItem.resource.externalArticle) {
                                return signingUpItem.resource.externalArticle.id !== signingUp.resource.externalArticle.id
                            } else {
                                return true
                            }
                        }),
                        signingUp
                    ]
                }
            )

            const modifiedEventDetailQueries = isMySigningUp(signingUp)
                ? setQueriesData<ApiModel<'Event'>>(
                    // The active event details query
                    queries.events.detail(eventId.value).queryKey,
                    // Update the event with the signing up
                    (previous) =>
                        useModelFactory('Event').create({
                            ...previous,
                            mySigningUp: signingUp
                        })
                )
                : []

            return {
                modified: [...modfiedSigningUpQueries, ...modifiedEventDetailQueries]
            }
        },

        onSuccess: (signingUp) => {
            const modfiedSigningUpQueries = setQueriesData<ApiModel<'SigningUp'>[]>(
                // The query key
                queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                // Replace the old siging uo with the updated version
                (previous) => {
                    return [
                        ...previous.filter((signingUpItem) => {
                            if (signingUpItem.resource?.externalArticle) {
                                return signingUpItem.resource.externalArticle.id !== signingUp.resource.externalArticle.id
                            } else {
                                return true
                            }
                        }),
                        signingUp
                    ]
                }
            )

            const modifiedEventDetailQueries = isMySigningUp(signingUp)
                ? setQueriesData<ApiModel<'Event'>>(
                    // The active event details query
                    queries.events.detail(eventId.value).queryKey,
                    // Update the event with the signing up
                    (previous) =>
                        useModelFactory('Event').create({
                            ...previous,
                            mySigningUp: signingUp
                        })
                )
                : []

            return {
                modified: [...modfiedSigningUpQueries, ...modifiedEventDetailQueries]
            }
        },

        onError: (_, __, { modified }) => {
            modified.forEach(([key, data]) => {
                queryClient.setQueryData(key, data)
            })
        },

        onSettled() {
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value)._ctx.signingUps().queryKey,
                refetchType: 'none',
                exact: true
            })

            // Invalidate event because we probably changed "mySigingUp"
            queryClient.invalidateQueries({
                queryKey: queries.events.detail(eventId.value).queryKey,
                refetchType: 'none',
                exact: true
            })

            // Reload list because we probably changed "mySigingUp"
            queryClient.invalidateQueries({
                queryKey: queries.events.list._def,
                refetchType: 'active',
                exact: false
            })
        }
    })

    return {
        createEventPost,
        updateEventPost,
        removeEventPost,

        createSigningUp,
        updateSigningUp,
        removeSigningUp,

        createResourceSetting,
        updateResourceSetting,
        removeResourceSetting,

        createRegistration,
        updateRegistration,
        removeRegistration,
        replaceRegistrations,

        createRegistrator,
        removeRegistrator,

        createResponsible,
        removeResponsible,

        createLocation,
        updateLocation,
        removeLocation,

        createDocument,
        updateDocument,
        removeDocument,

        cancelEvent,
        reactivateEvent,
        closeEvent,
        openEvent,
        removeEvent,

        updateBasicData,

        updateDresscodes,
        updateCaterings,
        updateOrganizer,

        updateInternalPublication,
        addInternalPublication,
        removeInternalPublication,

        updateExternalMaterial
    }
}

export function useCommitEventMutation(event: ApiModel<'Event'>) {
    /**
     * Generate and "import" all mutation queries
     */
    const mutations = useEventMutations(event)

    /**
     * Provide a convenient way to call event mutations without the hassle of naming conflicts
     *
     * @param mutation
     * @param payload
     * @param options
     */
    function commit<T extends keyof typeof mutations, P extends Parameters<(typeof mutations)[T]>[0], O extends Parameters<(typeof mutations)[T]>[1]>(
        mutation: T,
        payload?: P,
        options?: O
    ): Promise<ReturnType<(typeof mutations)[T]>> {
        return mutations[mutation](payload as any | void, options as any)
    }

    return commit
}
