import { format, parseISO, isValid } from 'date-fns'

type PickerRef = {
    [key: string]: HTMLInputElement
}

export function useDateTimePicker(modelValue: Ref<Date | null>) {
    const isKeyboardInteraction = ref(false)
    const isMouseInteraction = ref(false)

    const date = ref<string>(modelValue.value && format(modelValue.value, 'yyyy-MM-dd'))
    const time = ref<string>(modelValue.value && format(modelValue.value, 'HH:mm'))

    const timeRef = ref<PickerRef | null>(null)

    const timeChanged = ref(false)
    const dateChanged = ref(false)

    watch(modelValue, (newValue) => {
        console.log('modelValueChange')
        if (timeChanged.value) {
            console.log('time modelValueChange')
            time.value = null
            timeChanged.value = false
            dateChanged.value = false
            return
        }
        if (dateChanged.value) {
            console.log('date modelValueChange')
            date.value = null
            timeChanged.value = false
            dateChanged.value = false
            return
        }
        if (!newValue || !isValid(newValue)) {
            
            modelValue.value = null
            date.value = null
            time.value = null
            timeChanged.value = false
            dateChanged.value = false
            return
        }


        date.value = format(newValue, 'yyyy-MM-dd')
        time.value = format(newValue, 'HH:mm')
    })

    watch(date, (newDate) => {
        console.log('date changed', newDate)
        dateChanged.value = true
        if (!newDate || !time.value) {
            modelValue.value = null
            return
        }

        const dateTimeString = `${newDate}T${time.value}`
        const parsedDate = parseISO(dateTimeString)

        if (!isValid(parsedDate)) {
            modelValue.value = null
            return
        }

        modelValue.value = parsedDate
    })

    watch(time, (newTime) => {
        timeChanged.value = true
        if (!date.value || !newTime) {
            modelValue.value = null
            return
        }

        const dateTimeString = `${date.value}T${newTime}`
        const parsedDate = parseISO(dateTimeString)

        if (!isValid(parsedDate)) {
            modelValue.value = null
            return
        }

        modelValue.value = parsedDate
    })

    const onKeyboardDown = () => {
        isKeyboardInteraction.value = true
    }

    const onMouseDown = () => {
        isKeyboardInteraction.value = false
        isMouseInteraction.value = true
    }

    const onDateChange = () => {
        if (isKeyboardInteraction.value) {
            return
        }

        if (timeRef.value?.timeInputRef) {
            timeRef.value.timeInputRef.focus()
        }

        isKeyboardInteraction.value = false
        isMouseInteraction.value = false
    }

    return {
        date,
        time,
        timeRef,
        onDateChange,
        onKeyboardDown,
        onMouseDown
    }
}
