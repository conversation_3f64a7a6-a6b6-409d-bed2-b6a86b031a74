import { format, parseISO, isValid } from 'date-fns'

type PickerRef = {
    [key: string]: HTMLInputElement
}

export function useDateTimePicker(modelValue: Ref<Date | null>) {
    const isKeyboardInteraction = ref(false)
    const isMouseInteraction = ref(false)

    const date = ref<string | null>(modelValue.value ? format(modelValue.value, 'yyyy-MM-dd') : null)
    const time = ref<string | null>(modelValue.value ? format(modelValue.value, 'HH:mm') : null)

    const timeRef = ref<PickerRef | null>(null)

    // Track if changes are coming from external model updates (like reset)
    const isExternalUpdate = ref(false)

    watch(modelValue, (newValue) => {

        if (isExternalUpdate.value) {
            isExternalUpdate.value = false
            if (!newValue || !isValid(newValue)) {
                date.value = null
                time.value = null
            } else {
                date.value = format(newValue, 'yyyy-MM-dd')
                time.value = format(newValue, 'HH:mm')
            }
            return
        }

        // If model becomes null but we still have input values, keep them
        // This allows individual clearing while preserving the other input
        if (!newValue || !isValid(newValue)) {
            // Don't clear the input values here - let them persist
            return
        }

        // Update inputs when model has a valid value
        date.value = format(newValue, 'yyyy-MM-dd')
        time.value = format(newValue, 'HH:mm')
    })

    watch(date, (newDate) => {
        // Only create a valid datetime if both date and time are present
        if (!newDate || !time.value) {
            modelValue.value = null
            return
        }

        const dateTimeString = `${newDate}T${time.value}`
        const parsedDate = parseISO(dateTimeString)

        if (!isValid(parsedDate)) {
            modelValue.value = null
            return
        }

        modelValue.value = parsedDate
    })

    watch(time, (newTime) => {
        // Only create a valid datetime if both date and time are present
        if (!date.value || !newTime) {
            modelValue.value = null
            return
        }

        const dateTimeString = `${date.value}T${newTime}`
        const parsedDate = parseISO(dateTimeString)

        if (!isValid(parsedDate)) {
            modelValue.value = null
            return
        }

        modelValue.value = parsedDate
    })

    const onKeyboardDown = () => {
        isKeyboardInteraction.value = true
    }

    const onMouseDown = () => {
        isKeyboardInteraction.value = false
        isMouseInteraction.value = true
    }

    const onDateChange = () => {
        if (isKeyboardInteraction.value) {
            return
        }

        if (timeRef.value?.timeInputRef) {
            timeRef.value.timeInputRef.focus()
        }

        isKeyboardInteraction.value = false
        isMouseInteraction.value = false
    }

    // Reset functions for individual components
    const resetDate = () => {
        date.value = null
        // Model becomes null when date is cleared, but time input persists
        modelValue.value = null
    }

    const resetTime = () => {
        time.value = null
        // Model becomes null when time is cleared, but date input persists
        modelValue.value = null
    }

    // Reset both date and time
    const resetDateTime = () => {
        isExternalUpdate.value = true
        date.value = null
        time.value = null
        modelValue.value = null
    }

    return {
        date,
        time,
        timeRef,
        onDateChange,
        onKeyboardDown,
        onMouseDown,
        resetDate,
        resetTime,
        resetDateTime
    }
}
