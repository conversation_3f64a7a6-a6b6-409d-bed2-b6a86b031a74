import {
    eachDayOfInterval,
    endOfMonth,
    format,
    startOfToday,
    endOfWeek,
    add,
    startOfWeek,
    startOfMonth,
    getWeek,
    addDays,
    isSameDay,
    isSameMonth
} from 'date-fns'

export function useCalendarDates() {
    const WEEK_STARTS_ON = 1
    const today = startOfToday()

    const selectedDay = ref<Date>(today)
    const currentWeekStart = ref<Date>(startOfWeek(today, { weekStartsOn: WEEK_STARTS_ON }))
    const currentMonthDate = ref<Date>(startOfMonth(today))

    const currentWeekNumber = computed(() => getWeek(currentWeekStart.value, { weekStartsOn: WEEK_STARTS_ON }))

    const weekStart = computed(() => startOfWeek(currentWeekStart.value, { weekStartsOn: WEEK_STARTS_ON }))

    const weekEnd = computed(() => endOfWeek(currentWeekStart.value, { weekStartsOn: WEEK_STARTS_ON }))

    const firstDayCurrentMonth = computed(() => startOfMonth(currentMonthDate.value))

    const weekDates = computed(() => eachDayOfInterval({ start: weekStart.value, end: weekEnd.value }))

    const weekdays = computed(() => Array.from({ length: 7 }, (_, i) => addDays(startOfWeek(new Date(), { weekStartsOn: WEEK_STARTS_ON }), i)))

    const days = computed(() =>
        eachDayOfInterval({
            start: startOfWeek(firstDayCurrentMonth.value, { weekStartsOn: WEEK_STARTS_ON }),
            end: endOfWeek(endOfMonth(currentMonthDate.value), { weekStartsOn: WEEK_STARTS_ON })
        })
    )

    const formatters = {
        iso: (date: Date) => format(date, 'yyyy-MM-dd'),
        monthLabel: (date: Date) => format(date, 'MMM yyyy'),
        monthOnly: (date: Date) => format(date, 'MMM'),
        yearOnly: (date: Date) => format(date, 'yyyy')
    }

    const formattedDateISO = (date: Date) => formatters.iso(date)
    const formattedDateLabel = (date: Date) => formatters.monthLabel(date)
    const formattedCurrentMonth = computed(() => formatters.monthLabel(currentMonthDate.value))

    const formattedCurrentWeek = computed(() => {
        const start = weekStart.value
        const end = weekEnd.value

        if (isSameMonth(start, end)) {
            return formatters.monthLabel(start)
        } else {
            const startMonth = formatters.monthOnly(start)
            const endMonth = formatters.monthOnly(end)
            const startYear = formatters.yearOnly(start)
            const endYear = formatters.yearOnly(end)

            return startYear === endYear ? `${startMonth} - ${endMonth} ${startYear}` : `${startMonth} ${startYear} - ${endMonth} ${endYear}`
        }
    })

    function updateDateState(date: Date) {
        currentMonthDate.value = startOfMonth(date)
        currentWeekStart.value = startOfWeek(date, { weekStartsOn: WEEK_STARTS_ON })
    }

    function selectDay(day: Date) {
        if (!isSameDay(selectedDay.value, day)) {
            selectedDay.value = day
            updateDateState(day)
        }
    }

    function setMonth(direction: number) {
        const monthToSet = add(currentMonthDate.value, { months: direction })
        selectedDay.value = add(selectedDay.value, { months: direction })
        updateDateState(monthToSet)
    }

    function setWeek(direction: number) {
        const weekToSet = add(currentWeekStart.value, { weeks: direction })
        selectedDay.value = add(selectedDay.value, { weeks: direction })
        updateDateState(weekToSet)
    }

    return {
        today,
        days,
        weekDates,
        weekdays,
        currentWeekNumber,
        selectedDay,
        currentMonthDate,

        firstDayCurrentMonth,
        formattedCurrentMonth,
        formattedCurrentWeek,

        formattedDateISO,
        formattedDateLabel,

        setMonth,
        setWeek,
        selectDay
    }
}
