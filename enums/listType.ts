/**
 * <PERSON><PERSON><PERSON> https://handbuch.drkserver.org/pages/viewpage.action?pageId=301433030
 */
enum ListType {
    /**
     * -- VALUELIST_STATUSATDRK
     */
    MemberStatus = 1,

    /**
     * -- VALUELIST_GROUP_CATEGORY
     */

    GroupCategory = 10,

    /**
     * -- VALUELIST_OPERATION_CATEGORY
     */

    OperationCategory = 11,

    /**
     * -- VALUELIST_USERDRIVERSLICENCE
     */
    DriversLicense = 12,

    /**
     * -- VALUELIST_FUNCTION
     */

    Function = 14,

    /**
     * Event: Description
     * -- VALUELIST_SERVICELOG_DESCRIPTION
     */
    ServiceLogDescription = 20,

    /**
     * Event: Operation Type
     * -- VALUELIST_OPERATION_TYPE
     */
    OperationType = 40,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_KIND
     */
    QualificationType = 47,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_LEADINGSHIP
     */
    Leadingship = 48,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_AMBULANCE
     */
    Ambulance = 49,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_CARE
     */
    SupportService = 50,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_IUK
     */
    ITService = 51,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_MOUNTAINRESCUE
     */
    MountainRescue = 52,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_WATERRESCUE
     */
    WaterRescue = 53,

    /**
     * -- VALUELIST_EVENT_TYPE
     */
    EventType = 62,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_ABC
     */
    ABCService = 66,

    /**
     * -- VALUELIST_OPERATIONQUALIFICATION_QUALI_PSNV
     */
    PSNV = 69,

    /**
     * -- VALUELIST_MEMBERSHIPENTRY_E
     */

    MembershipentryE = 70,

    /**
     * -- VALUELIST_MEMBERSHIPENTRY_FD
     */

    MembershipentryFD = 71,

    /**
     * -- VALUELIST_MEMBERSHIPENTRY_F
     */

    MembershipentryF = 72,

    /**
     * -- VALUELIST_MEMBERSHIPENTRY_S
     */

    MembershipentryS = 73,

    /**
     * -- VALUELIST_MEMBERSHIPENTRY_UH
     */

    MembershipentryUH = 74,

    /**
     * -- VALUELIST_MEMBERSHIPENTRY_EH
     */

    MembershipentryEH = 75,

    /**
     * -- VALUELIST_TYPEOFMEMBERSHIP_E
     */

    TypeOfMembershipE = 80,

    /**
     * -- VALUELIST_TYPEOFMEMBERSHIP_FD
     */

    TypeOfMembershipFD = 81,

    /**
     * -- VALUELIST_TYPEOFMEMBERSHIP_F
     */

    TypeOfMembershipF = 82,

    /**
     * -- VALUELIST_TYPEOFMEMBERSHIP_S
     */

    TypeOfMembershipS = 83,

    /**
     * -- VALUELIST_TYPEOFMEMBERSHIP_UH
     */

    TypeOfMembershipUH = 84,

    /**
     * -- VALUELIST_TYPEOFMEMBERSHIP_EH
     */

    TypeOfMembershipEH = 85,

    /**
     * -- VALUELIST_TYPEOFMEMBERSHIP_H
     */

    TypeOfMembershipH = 86,

    /**
     * Event: Extended Description
     * -- VALUELIST_EVENT_EXTENDED_DESCRIPTION
     */
    EventExtendedDescription = 89,

    /**
     * -- VALUELIST_OPERATION_CATEGORY_DESC
     */

    OperationCategoryDesc = 101,

    /**
     *  -- VALUELIST_GROUP_CATEGORY_DESC_BUND
     */

    GroupCategoryDescBound = 146,

    /**
     *  -- VALUELIST_GROUP_CATEGORY_DESC_JRK
     */

    GroupCategoryDescJrk = 147,

    /**
     *  -- VALUELIST_GROUP_CATEGORY_DESC_LA
     */

    GroupCategoryDescLa = 148,

    /**
     *  -- VALUELIST_GROUP_CATEGORY_DESC_KA
     */

    GroupCategoryDescKa = 149,

    /**
     *  -- VALUELIST_GROUP_CATEGORY_DESC_RKG
     */

    GroupCategoryDescRkg = 180,

    /**
     *  -- VALUELIST_GROUP_CATEGORY_DESC_PV
     */

    GroupCategoryDescPv = 181,

    /**
     * -- VALUELIST_TV_MODULE
     */
    Module = 221,

    /**
     * -- VALUELIST_TV_MODULE_KIND
     */
    ModuleKind = 222,

    /**
     * -- VALUELIST_TV_MODULE_TYPE
     */
    ModuleType = 223,

    /**
     *  -- VALUELISTE_GENERIC_01
     */

    Department = 401,

    /**
     *  -- VALUELISTE_GENERIC_02
     */

    AgeAndHonorCamaraderie = 402,

    /**
     *  -- VALUELISTE_GENERIC_03
     */

    WorkingCircle = 403,

    /**
     *  -- VALUELISTE_GENERIC_05
     */

    CommitteeGroupOvLevel = 405,

    /**
     *  -- VALUELISTE_GENERIC_06
     */

    CommitteeGroupDistrictLevel = 406,

    /**
     *  -- GVALUELISTE_ENERIC_07
     */

    BloodDonationSystem = 407,

    /**
     *  -- VALUELISTE_GENERIC_09
     */

    Group = 409,

    /**
     *  -- VALUELISTE_GENERIC_12
     */

    PersonalInformation = 412,

    /**
     *  -- VALUELISTE_GENERIC_13
     */

    Instructor = 413,

    /**
     *  -- VALUELISTE_GENERIC_14
     */

    ProjectGroup = 414,

    /**
     *  -- VALUELISTE_GENERIC_15
     */

    ServiceDog = 415,

    /**
     *  -- VALUELISTE_GENERIC_19
     */

    ForeignAid = 419,

    /**
     *  -- VALUELISTE_GENERIC_20
     */

    Management = 420,

    /**
     * -- VALUELISTE_GENERIC_21
     */

    InformationOnPersons = 421,

    /**
     * -- VALUELISTE_GENERIC_22
     */

    YouthRedCross = 422,

    /**
     * -- VALUELISTE_GENERIC_24
     */

    Readiness = 424,

    /**
     * -- VALUELISTE_GENERIC_25
     */

    LocalGroup = 425,

    /**
     * -- VALUELISTE_GENERIC_26
     */

    CrisisManagement = 426,

    /**
     * -- VALUELISTE_GENERIC_27
     */
    OtherOperationQualification = 427,

    /**
     * -- VALUELISTE_GENERIC_28
     */
    TechAndSafety = 428,

    /**
     * -- VALUELISTE_GENERIC_29
     */
    NursingService = 429,

    /**
     * -- VALUELISTE_GENERIC_30
     */

    Lifeguards = 430,

    /**
     * -- VALUELISTE_GENERIC_31
     */

    EmergencyServices = 431,

    /**
     * -- VALUELISTE_GENERIC_32
     */

    MountainRescueService = 432,

    /**
     * -- VALUELISTE_GENERIC_33
     */

    InformationOffice = 433,

    /**
     * -- VALUELISTE_GENERIC_34
     */

    MissingPersonTracingService = 434,

    /**
     * -- VALUELISTE_GENERIC_35
     */

    GenericBRK = 435,

    /**
     * -- VALUELISTE_GENERIC_40
     */
    UnboundVolunteers = 440,

    /**
     * -- VALUELIST_EVENT_REASONOFCANCELLATION
     */
    EventCancelationReason = 601,

    /**
     * Event: Signing Up Status
     * -- VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS
     */
    SigningUpStatus = 602,

    /**
     * -- VALUELIST_EVENT_DRESSCODE
     */
    Dresscode = 604,

    /**
     * -- VALUELIST_EVENT_CATERING
     */
    EventCatering = 605,

    /**
     * -- VALUELIST_EVENT_DOCUMENT_TYPE
     */
    DocumentType = 607,

    /**
     * -- VALUELIST_EVENT_DOCUMENT_PERMISSION
     */
    DocumentPermission = 608,
}

export default ListType;
