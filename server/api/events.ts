import defu from 'defu'
import { pickBy } from 'lodash-es'
import { useSearchParams } from '~/composables/search-params'

type Result = {
    totalItems: number
    offset: number
    limit: number
    items: object[]
}

const allowedFilterParams = [
    'start',
    'end',
    'offset',
    'limit',
    'type',
    'extendedDescription',
    'executedByMyOrganisations',
    'executedBy',
    'withOpenEventPosts',
    'responseStatus',
    'assignedToEventPost',
    'assignedToEventPostWithExtendedPermissions',
    'responsibleFor',
    'withDecisionsOfChainOfCommand',
    'searchTerm',
    'registratorFor',
    'status',
    'sortBy'
]

const filterSearchParams = function (input: Record<string, any>) {
    return pickBy(input, (_value, key) => allowedFilterParams.includes(key))
}

const isResult = (input: any): input is Result => {
    return typeof input === 'object' && 'totalItems' in input && 'offset' in input && 'limit' in input && 'items' in input
}

export default defineEventHandler(async (httpEvent) => {
    const { post: fetchEvents } = await useApiProxy(httpEvent)


    /**
     * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/events/EventSearchCriteria.yaml
     * */
    const defaults = {
        assignedToEventPost: false,

        assignedToEventPostWithExtendedPermissions: false,

        // @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/events/EventType.yaml
        // ALL, EVENT, APPRENTICESHIP
        type: 'ALL',

        offset: 0,

        // We are allowed to omit date fields
        // start: "2020-01-01T00:00:00Z",
        // end: new Date(),

        withDecisionsOfChainOfCommand: false,

        withOpenEventPosts: false,

        // @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/events/EventResponseType.yaml
        // ALL, INVITED, ANSWERED, CONFIRMED, DENIED
        responseStatus: 'ALL',

        responsibleFor: false,

        registratorFor: false,

        // @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/general/Organisation.yaml
        // @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/general/Organisations.yaml
        // `id` and `name` are required but `name` can be any string (including empty strings).
        // Example: [{ id: 9347, name: "" }]
        executedBy: [],

        searchTerm: null,

        status: [],

        extendedDescription: null,

        organizers: [],

        executedByMyOrganisations: null,

        limit: 10,

        sortBy: []
    }

    const searchParams = defu(filterSearchParams(getQuery(httpEvent)), defaults)

    // searchParams.start = new Date(searchParams.start)
    // searchParams.end = new Date(searchParams.end)

    // Sanitize query parameter `executedBy``
    // - Force `string | any[]` to an array
    // - Build a minimal `organisation` object (We just need the ID attribute here. Name can be an empty string)
    searchParams.executedBy =[].concat(searchParams.executedBy).map(item => {
        const parseItem = JSON.parse(item)
        return {
            id: parseInt(parseItem.id),
            name: parseItem.label
        }
    })

    searchParams.sortBy = useSearchParams(searchParams.sortBy).deserializeObjectToArr()

    searchParams.status = [].concat(searchParams.status).map((item) => item)

    /**
     * The search parameter `extendedDescription` seems to have no effect;
     * So we map that value to `searchTerm`
     */
    if (!!searchParams.extendedDescription) {
        searchParams.searchTerm = searchParams.extendedDescription
    }

    return await fetchEvents<Result>('/events/search', searchParams, {
        // @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/parameters/events/EventFieldParameter.yaml
        fields: [
            'EVENT',
            'EVENT_LOCATION',
            'ORGANIZER',
            'RESPONSIBLE_PERSONS',
            'DOCUMENTS',
            'MY_SIGNING_UP',
            'EVENT_POSTS',
            'DETAILED_PERMISSION',
            'SHARE_REQUESTS',
            'SHARE_REQUESTS_RECEIVED'
        ],
        fetchPermissions: false
    })
})
